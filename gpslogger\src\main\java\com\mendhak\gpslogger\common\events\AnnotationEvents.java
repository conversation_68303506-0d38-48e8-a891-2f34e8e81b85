/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.common.events;

public class AnnotationEvents {

    /**
     * Event fired when the annotation button count setting changes
     */
    public static class ButtonCountChanged {
        public final int newButtonCount;

        public ButtonCountChanged(int newButtonCount) {
            this.newButtonCount = newButtonCount;
        }
    }

    /**
     * Event fired when annotation layout style settings change
     */
    public static class LayoutStyleChanged {
        public final String layoutStyle;
        public final String spacingMode;
        public final String viewMode;

        public LayoutStyleChanged(String layoutStyle, String spacingMode, String viewMode) {
            this.layoutStyle = layoutStyle;
            this.spacingMode = spacingMode;
            this.viewMode = viewMode;
        }
    }

    /**
     * Event fired when annotation template processing is complete and audio file should be renamed
     */
    public static class AudioFileRename {
        public final String originalVoiceText;
        public final String finalAnnotationText;
        public final String buttonName;
        public final int buttonIndex;
        public final String groupName;

        public AudioFileRename(String originalVoiceText, String finalAnnotationText,
                              String buttonName, int buttonIndex, String groupName) {
            this.originalVoiceText = originalVoiceText;
            this.finalAnnotationText = finalAnnotationText;
            this.buttonName = buttonName;
            this.buttonIndex = buttonIndex;
            this.groupName = groupName;
        }
    }
}
