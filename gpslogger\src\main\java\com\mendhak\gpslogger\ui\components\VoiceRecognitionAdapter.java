/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 语音识别适配器 - 简化版本，只用于语音转文字
 */
public class VoiceRecognitionAdapter {
    private static final Logger LOG = Logs.of(VoiceRecognitionAdapter.class);
    
    private Context context;
    private PreferenceHelper preferenceHelper;
    private Handler mainHandler;
    
    // 语音识别相关
    private SpeechRecognizer speechRecognizer;
    private AtomicBoolean isRecognizing = new AtomicBoolean(false);
    
    // 监听器
    private VoiceRecognitionListener listener;
    
    // 请求代码常量
    private static final int XUNFEI_RECOGNITION_REQUEST_CODE = 1002;
    
    /**
     * 语音识别监听器接口
     */
    public interface VoiceRecognitionListener {
        void onRecognitionResult(String text);
        void onRecognitionError(String error);
        void onRecognitionTimeout();
    }
    
    /**
     * 构造函数
     */
    public VoiceRecognitionAdapter(Context context) {
        this.context = context;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        initializeSpeechRecognizer();
        LOG.debug("VoiceRecognitionAdapter initialized (simplified version)");
    }
    
    /**
     * 设置识别监听器
     */
    public void setRecognitionListener(VoiceRecognitionListener listener) {
        this.listener = listener;
    }
    
    /**
     * 初始化语音识别器
     */
    private void initializeSpeechRecognizer() {
        try {
            if (SpeechRecognizer.isRecognitionAvailable(context)) {
                speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context);
                LOG.debug("SpeechRecognizer created successfully");
            } else {
                LOG.warn("Speech recognition not available on this device");
            }
        } catch (Exception e) {
            LOG.error("Failed to initialize speech recognizer", e);
        }
    }
    
    /**
     * 开始语音识别
     */
    public boolean startRecognition() {
        if (isRecognizing.get()) {
            LOG.warn("Speech recognition already in progress");
            return false;
        }

        if (speechRecognizer == null) {
            LOG.error("Speech recognizer not initialized");
            if (listener != null) {
                listener.onRecognitionError("语音识别组件初始化失败");
            }
            return false;
        }

        try {
            Intent intent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, "zh-CN");
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, "zh-CN");
            intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5);
            intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, context.getPackageName());
            intent.putExtra(RecognizerIntent.EXTRA_PROMPT, "请说话");

            speechRecognizer.setRecognitionListener(new android.speech.RecognitionListener() {
                @Override
                public void onReadyForSpeech(android.os.Bundle params) {
                    LOG.debug("Speech recognizer ready for speech");
                }

                @Override
                public void onBeginningOfSpeech() {
                    LOG.debug("Beginning of speech detected");
                }

                @Override
                public void onRmsChanged(float rmsdB) {
                    // 音量变化
                }

                @Override
                public void onBufferReceived(byte[] buffer) {
                    // 接收到音频缓冲区数据
                }

                @Override
                public void onEndOfSpeech() {
                    LOG.debug("End of speech detected");
                }

                @Override
                public void onError(int error) {
                    LOG.error("Speech recognition error: {}", error);
                    isRecognizing.set(false);
                    if (listener != null) {
                        listener.onRecognitionError("语音识别错误: " + error);
                    }
                }

                @Override
                public void onResults(android.os.Bundle results) {
                    LOG.debug("Speech recognition results received");
                    isRecognizing.set(false);
                    
                    java.util.ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
                    if (matches != null && !matches.isEmpty()) {
                        String result = matches.get(0);
                        LOG.info("Speech recognition result: '{}'", result);
                        if (listener != null) {
                            listener.onRecognitionResult(result);
                        }
                    } else {
                        LOG.warn("No speech recognition results");
                        if (listener != null) {
                            listener.onRecognitionError("没有识别到语音内容");
                        }
                    }
                }

                @Override
                public void onPartialResults(android.os.Bundle partialResults) {
                    // 部分结果
                }

                @Override
                public void onEvent(int eventType, android.os.Bundle params) {
                    // 其他事件
                }
            });

            speechRecognizer.startListening(intent);
            isRecognizing.set(true);
            LOG.info("Speech recognition started successfully");
            return true;

        } catch (Exception e) {
            LOG.error("Failed to start speech recognition", e);
            if (listener != null) {
                listener.onRecognitionError("启动语音识别失败: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * 停止语音识别
     */
    public void stopRecognition() {
        if (speechRecognizer != null && isRecognizing.get()) {
            speechRecognizer.stopListening();
            isRecognizing.set(false);
            LOG.debug("Speech recognition stopped");
        }
    }
    
    /**
     * 取消语音识别
     */
    public void cancelRecognition() {
        if (speechRecognizer != null && isRecognizing.get()) {
            speechRecognizer.cancel();
            isRecognizing.set(false);
            LOG.debug("Speech recognition cancelled");
        }
    }
    
    /**
     * 接收音频数据（简化版本，不处理）
     */
    public void feedAudioData(byte[] audioData, int length) {
        // 简化版本不处理音频数据
    }
    
    /**
     * 检查语音识别是否真正可用
     */
    public boolean isSpeechRecognitionReallyAvailable() {
        return SpeechRecognizer.isRecognitionAvailable(context);
    }
    
    /**
     * 处理Activity结果
     */
    public void handleActivityResult(int requestCode, int resultCode, Intent data) {
        // 简化版本不处理Activity结果
        LOG.debug("handleActivityResult called with requestCode: {}, resultCode: {}", requestCode, resultCode);
    }
    
    /**
     * 获取讯飞请求代码
     */
    public static int getXunfeiRequestCode() {
        return XUNFEI_RECOGNITION_REQUEST_CODE;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
            speechRecognizer = null;
        }
        isRecognizing.set(false);
        LOG.debug("VoiceRecognitionAdapter cleaned up");
    }
}
