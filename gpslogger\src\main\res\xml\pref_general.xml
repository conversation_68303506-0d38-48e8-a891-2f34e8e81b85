<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <SwitchPreferenceCompat
        android:key="startonbootup"
        android:summary="@string/startonbootup_summary"
        android:title="@string/startonbootup_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="startonapplaunch"
        android:summary="@string/startonapplaunch_summary"
        android:title="@string/startonapplaunch_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="useImperial"
        android:summary="@string/display_imperial_summary"
        android:title="@string/display_imperial_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="haptic_feedback_enabled"
        android:defaultValue="true"
        android:summary="@string/haptic_feedback_summary"
        android:title="@string/haptic_feedback_title"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:key="haptic_feedback_intensity"
        android:title="@string/haptic_feedback_intensity_title"
        android:summary="@string/haptic_feedback_intensity_summary"
        android:entries="@array/haptic_feedback_intensity_entries"
        android:entryValues="@array/haptic_feedback_intensity_values"
        android:defaultValue="MEDIUM"
        android:dependency="haptic_feedback_enabled"
        app:iconSpaceReserved="false" />

    <!-- Audio Feedback Settings -->
    <PreferenceCategory
        android:title="@string/audio_feedback_category_title"
        app:iconSpaceReserved="false">

        <ListPreference
            android:key="audio_feedback_type"
            android:title="@string/audio_feedback_type_title"
            android:summary="@string/audio_feedback_type_summary"
            android:entries="@array/audio_feedback_type_entries"
            android:entryValues="@array/audio_feedback_type_values"
            android:defaultValue="NONE"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="audio_feedback_custom_file"
            android:title="@string/audio_feedback_custom_file_title"
            android:summary="@string/audio_feedback_custom_file_summary"
            android:dependency="audio_feedback_type"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="audio_feedback_test"
            android:title="@string/audio_feedback_test_title"
            android:summary="@string/audio_feedback_test_summary"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="audio_feedback_voice_enabled"
            android:title="@string/audio_feedback_voice_enabled_title"
            android:summary="@string/audio_feedback_voice_enabled_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="audio_feedback_text_enabled"
            android:title="@string/audio_feedback_text_enabled_title"
            android:summary="@string/audio_feedback_text_enabled_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="audio_feedback_counter_enabled"
            android:title="@string/audio_feedback_counter_enabled_title"
            android:summary="@string/audio_feedback_counter_enabled_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <ListPreference
        android:key="coordinatedisplayformat"
        android:title="@string/coordinate_display_format"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="hide_notification_buttons"
        android:summary="@string/restart_required"
        android:title="@string/hide_notification_buttons"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="hide_notification_from_lock_screen"
        android:title="@string/hide_notification_from_lock_screen"
        android:summary="@string/restart_required"
        android:defaultValue="true"
        app:iconSpaceReserved="false"/>`

    <SwitchPreferenceCompat
        android:defaultValue="false"
        android:key="hide_notification_from_status_bar"
        android:summary="@string/restart_required"
        android:title="@string/hide_notification_from_status_bar"
        app:iconSpaceReserved="false" />

    <SeekBarPreference
        android:key="annotations_button_count"
        android:title="@string/annotation_button_count_title"
        android:summary="@string/annotation_button_count_summary"
        android:defaultValue="9"
        android:min="9"
        android:max="25"
        app:iconSpaceReserved="false" />

    <PreferenceCategory
        android:title="@string/annotation_layout_category"
        app:iconSpaceReserved="false">

        <ListPreference
            android:key="annotation_layout_style"
            android:title="@string/annotation_layout_style_title"
            android:summary="@string/annotation_layout_style_summary"
            android:entries="@array/annotation_layout_style_entries"
            android:entryValues="@array/annotation_layout_style_values"
            android:defaultValue="rectangular"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="annotation_spacing_mode"
            android:title="@string/annotation_spacing_mode_title"
            android:summary="@string/annotation_spacing_mode_summary"
            android:entries="@array/annotation_spacing_mode_entries"
            android:entryValues="@array/annotation_spacing_mode_values"
            android:defaultValue="standard"
            app:iconSpaceReserved="false" />

        <SeekBarPreference
            android:key="annotation_custom_spacing"
            android:title="@string/annotation_custom_spacing_title"
            android:summary="@string/annotation_custom_spacing_summary"
            android:defaultValue="4"
            android:min="1"
            android:max="20"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="annotation_view_mode"
            android:title="@string/annotation_view_mode_title"
            android:summary="@string/annotation_view_mode_summary"
            android:entries="@array/annotation_view_mode_entries"
            android:entryValues="@array/annotation_view_mode_values"
            android:defaultValue="grid"
            app:iconSpaceReserved="false" />

        <!-- Voice input settings -->
        <SwitchPreferenceCompat
            android:key="voice_input_enabled"
            android:title="@string/pref_voice_input_enabled_title"
            android:summary="@string/pref_voice_input_enabled_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="voice_input_language"
            android:title="@string/pref_voice_input_language_title"
            android:summary="@string/pref_voice_input_language_summary"
            android:dependency="voice_input_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="voice_input_timeout"
            android:title="@string/pref_voice_input_timeout_title"
            android:summary="@string/pref_voice_input_timeout_summary"
            android:dependency="voice_input_enabled"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <Preference
        android:defaultValue="false"
        android:key="enableDisableGps"
        android:summary="@string/enabledisablegps_summary"
        android:title="@string/enabledisablegps_title"
        app:iconSpaceReserved="false" />

    <Preference
        android:key="install_conscrypt_provider"
        android:title="@string/install_conscrypt_provider_title"
        android:summary="@string/install_conscrypt_provider_summary"
        app:iconSpaceReserved="false"
        >
        <intent
            android:action="android.intent.action.VIEW"
            android:data="https://f-droid.org/en/packages/com.mendhak.conscryptprovider/" />
    </Preference>

    <SwitchPreferenceCompat
        android:defaultValue="false"
        android:key="debugtofile"
        android:summary="@string/debuglog_summary"
        android:title="@string/debuglog_title"
        app:iconSpaceReserved="false" />

    <Preference
        android:enabled="true"
        android:key="debuglogtoemail"
        android:summary="@string/debuglog_attach_to_email_summary"
        android:title="@string/debuglog_attach_to_email"
        app:iconSpaceReserved="false" />

    <!--https://www.flaticon.com/free-icon/dark-mode_5262027-->
    <ListPreference
        android:defaultValue="system"
        android:entries="@array/app_theme_options"
        android:entryValues="@array/app_theme_values"
        android:icon="@drawable/dark_mode"
        android:key="app_theme_setting"
        android:summary="@string/app_theme_setting"
        android:title="@string/app_theme_title"
        app:iconSpaceReserved="false" />

    <!--https://www.flaticon.com/free-icon/translate_889588-->
    <ListPreference
        android:icon="@drawable/translate"
        android:key="changelanguage"
        android:summary="@string/change_language_summary"
        android:title="@string/change_language_title"
        app:iconSpaceReserved="false" />

    <!--https://www.flaticon.com/free-icon/close_463612-->
    <Preference
        android:enabled="true"
        android:icon="@drawable/reset"
        android:key="resetapp"
        android:summary="@string/reset_app_summary"
        android:title="@string/reset_app_title"
        app:iconSpaceReserved="false" />

    <Preference
        android:key="about_version_info"
        app:iconSpaceReserved="false" />

    <Preference
        android:key="gpslogger_privacypolicy"
        android:title="@string/privacy_policy"
        app:iconSpaceReserved="false">
        <intent
            android:action="android.intent.action.VIEW"
            android:data="https://github.com/mendhak/gpslogger/blob/master/assets/text/privacypolicy.md#privacy-policy" />
    </Preference>

    <Preference
        android:icon="@drawable/coffee"
        android:key="gpsloggerdonate_link"
        android:title="@string/tip_coffee"
        app:iconSpaceReserved="false">
        <intent
            android:action="android.intent.action.VIEW"
            android:data="https://paypal.me/mendhak" />
    </Preference>

    <Preference
        android:icon="@drawable/github"
        android:key="gpslogger_repo"
        android:title="Source Code"
        app:iconSpaceReserved="false">
        <intent
            android:action="android.intent.action.VIEW"
            android:data="https://github.com/mendhak/gpslogger/" />
    </Preference>

    <Preference
        android:icon="@mipmap/gpsloggericon3"
        android:key="gpslogger_site"
        android:title="https://gpslogger.app"
        app:iconSpaceReserved="false">
        <intent
            android:action="android.intent.action.VIEW"
            android:data="https://gpslogger.app/" />
    </Preference>

    <Preference
        android:key="gpslogger_translations"
        android:title="Translations"
        app:iconSpaceReserved="false">
        <intent
            android:action="android.intent.action.VIEW"
            android:data="https://hosted.weblate.org/engage/gpslogger/" />
    </Preference>

    <Preference
        android:key="gpslogger_thirdparty"
        android:title="Open Source Libraries"
        app:iconSpaceReserved="false">
        <intent
            android:action="android.intent.action.VIEW"
            android:data="https://github.com/mendhak/gpslogger/blob/master/assets/text/opensource.md#open-source-libraries" />
    </Preference>

</PreferenceScreen>
