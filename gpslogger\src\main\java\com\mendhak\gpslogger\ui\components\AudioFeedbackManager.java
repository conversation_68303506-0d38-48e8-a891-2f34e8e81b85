/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.SoundPool;
import android.net.Uri;
import android.os.Build;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.PreferenceNames;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.LayoutEnums.TriggerMode;

import org.slf4j.Logger;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages audio feedback functionality for annotation buttons
 * Provides sound feedback for button clicks with customizable audio files
 */
public class AudioFeedbackManager {
    private static final Logger LOG = Logs.of(AudioFeedbackManager.class);
    
    private final Context context;
    private final PreferenceHelper preferenceHelper;
    private SoundPool soundPool;
    private MediaPlayer mediaPlayer;
    private final Map<AudioFeedbackType, Integer> soundIds = new HashMap<>();
    private boolean isInitialized = false;
    
    /**
     * Audio feedback types with built-in sounds
     */
    public enum AudioFeedbackType {
        NONE("无声音", 0),
        SYSTEM_NOTIFICATION("系统通知音", 1),
        SYSTEM_RINGTONE("系统铃声", 2),
        SYSTEM_ALARM("系统闹钟音", 3),
        TONE_BEEP("标准哔声", 4),
        TONE_CLICK("点击音", 5),
        TONE_ERROR("错误音", 6),
        TONE_SUCCESS("成功音", 7),
        TONE_WARNING("警告音", 8),
        TONE_DTMF_0("按键音0", 9),
        TONE_DTMF_1("按键音1", 10),
        TONE_DTMF_STAR("按键音*", 11),
        CUSTOM("自定义音频", 99);
        
        private final String displayName;
        private final int resourceId;
        
        AudioFeedbackType(String displayName, int resourceId) {
            this.displayName = displayName;
            this.resourceId = resourceId;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public int getResourceId() {
            return resourceId;
        }
        
        public static AudioFeedbackType fromString(String str) {
            if (str == null) return NONE;
            try {
                return AudioFeedbackType.valueOf(str.toUpperCase());
            } catch (IllegalArgumentException e) {
                return NONE;
            }
        }
    }
    
    /**
     * Button modes that can have audio feedback
     */
    public enum ButtonMode {
        VOICE_INPUT("语音模式"),
        TEXT_INPUT("文本模式"),
        COUNTER_ONLY("计数器模式");
        
        private final String displayName;
        
        ButtonMode(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static ButtonMode fromTriggerMode(TriggerMode triggerMode) {
            switch (triggerMode) {
                case VOICE_INPUT: return VOICE_INPUT;
                case TEXT_INPUT: return TEXT_INPUT;
                case COUNTER_ONLY: return COUNTER_ONLY;
                default: return VOICE_INPUT;
            }
        }
    }
    
    public AudioFeedbackManager(Context context) {
        this.context = context.getApplicationContext();
        this.preferenceHelper = PreferenceHelper.getInstance();
        initializeSoundPool();
    }
    
    /**
     * Initialize SoundPool for playing audio feedback
     */
    private void initializeSoundPool() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_ASSISTANCE_SONIFICATION)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .build();
                
                soundPool = new SoundPool.Builder()
                    .setMaxStreams(3)
                    .setAudioAttributes(audioAttributes)
                    .build();
            } else {
                soundPool = new SoundPool(3, AudioManager.STREAM_NOTIFICATION, 0);
            }
            
            loadBuiltInSounds();
            isInitialized = true;
            LOG.debug("AudioFeedbackManager initialized successfully");
            
        } catch (Exception e) {
            LOG.error("Failed to initialize AudioFeedbackManager", e);
            isInitialized = false;
        }
    }
    
    /**
     * Load built-in sound resources
     * Uses system sounds as fallback if custom audio files are not available
     */
    private void loadBuiltInSounds() {
        // For now, we'll use system notification sounds as placeholders
        // Users can later add custom audio files to res/raw/ directory

        // Create placeholder sound IDs using system notification sound
        try {
            // Load a simple notification sound as placeholder for all types
            // In a real implementation, you would have actual audio files in res/raw/
            for (AudioFeedbackType type : AudioFeedbackType.values()) {
                if (type.getResourceId() != 0) {
                    // For now, we'll mark these as loaded but use system sounds
                    soundIds.put(type, type.ordinal()); // Use ordinal as placeholder ID
                    LOG.debug("Registered placeholder for sound: {}", type.getDisplayName());
                }
            }
        } catch (Exception e) {
            LOG.warn("Failed to register sound placeholders", e);
        }
    }
    
    /**
     * Check if audio feedback is enabled for the given button mode
     */
    public boolean isAudioFeedbackEnabled(ButtonMode buttonMode) {
        if (!isInitialized) {
            return false;
        }
        
        switch (buttonMode) {
            case VOICE_INPUT:
                return preferenceHelper.getBoolean(PreferenceNames.AUDIO_FEEDBACK_VOICE_ENABLED, false);
            case TEXT_INPUT:
                return preferenceHelper.getBoolean(PreferenceNames.AUDIO_FEEDBACK_TEXT_ENABLED, false);
            case COUNTER_ONLY:
                return preferenceHelper.getBoolean(PreferenceNames.AUDIO_FEEDBACK_COUNTER_ENABLED, false);
            default:
                return false;
        }
    }
    
    /**
     * Get current audio feedback type setting
     */
    public AudioFeedbackType getAudioFeedbackType() {
        String typeStr = preferenceHelper.getString(PreferenceNames.AUDIO_FEEDBACK_TYPE,
                                                   AudioFeedbackType.TONE_BEEP.name());
        return AudioFeedbackType.fromString(typeStr);
    }
    
    /**
     * Play audio feedback for button click
     */
    public void playButtonClickFeedback(ButtonMode buttonMode) {
        if (!isAudioFeedbackEnabled(buttonMode)) {
            return;
        }
        
        AudioFeedbackType feedbackType = getAudioFeedbackType();
        
        if (feedbackType == AudioFeedbackType.CUSTOM) {
            playCustomAudio();
        } else {
            playBuiltInAudio(feedbackType);
        }
    }
    
    /**
     * Play built-in audio feedback using system sounds and ringtones
     */
    private void playBuiltInAudio(AudioFeedbackType feedbackType) {
        if (!isInitialized || feedbackType == AudioFeedbackType.NONE) {
            return;
        }

        try {
            switch (feedbackType) {
                case SYSTEM_NOTIFICATION:
                    playSystemSound(android.media.RingtoneManager.TYPE_NOTIFICATION);
                    break;
                case SYSTEM_RINGTONE:
                    playSystemSound(android.media.RingtoneManager.TYPE_RINGTONE);
                    break;
                case SYSTEM_ALARM:
                    playSystemSound(android.media.RingtoneManager.TYPE_ALARM);
                    break;
                case TONE_BEEP:
                    playToneGenerator(android.media.ToneGenerator.TONE_PROP_BEEP, 200);
                    break;
                case TONE_CLICK:
                    playToneGenerator(android.media.ToneGenerator.TONE_PROP_ACK, 100);
                    break;
                case TONE_ERROR:
                    playToneGenerator(android.media.ToneGenerator.TONE_CDMA_ABBR_ALERT, 300);
                    break;
                case TONE_SUCCESS:
                    playToneGenerator(android.media.ToneGenerator.TONE_PROP_PROMPT, 150);
                    break;
                case TONE_WARNING:
                    playToneGenerator(android.media.ToneGenerator.TONE_SUP_ERROR, 250);
                    break;
                case TONE_DTMF_0:
                    playToneGenerator(android.media.ToneGenerator.TONE_DTMF_0, 100);
                    break;
                case TONE_DTMF_1:
                    playToneGenerator(android.media.ToneGenerator.TONE_DTMF_1, 100);
                    break;
                case TONE_DTMF_STAR:
                    playToneGenerator(android.media.ToneGenerator.TONE_DTMF_S, 100);
                    break;
                default:
                    playSystemRingtone(android.media.RingtoneManager.TYPE_NOTIFICATION);
                    break;
            }
            LOG.debug("Played system audio feedback: {}", feedbackType.getDisplayName());
        } catch (Exception e) {
            LOG.error("Failed to play system audio feedback: {}", feedbackType.getDisplayName(), e);
        }
    }

    /**
     * Play system sound with multiple fallback methods
     */
    private void playSystemSound(int ringtoneType) {
        LOG.debug("Attempting to play system sound type: {}", ringtoneType);

        // Method 1: Try Ringtone API
        if (playSystemRingtone(ringtoneType)) {
            return;
        }

        // Method 2: Try MediaPlayer with system sound
        if (playSystemSoundWithMediaPlayer(ringtoneType)) {
            return;
        }

        // Method 3: Fallback to ToneGenerator
        LOG.warn("All system sound methods failed, falling back to tone generator");
        playToneGenerator(android.media.ToneGenerator.TONE_PROP_BEEP, 300);
    }

    /**
     * Play system ringtone (notification, ringtone, alarm) with improved error handling
     */
    private boolean playSystemRingtone(int ringtoneType) {
        try {
            LOG.debug("Attempting to play system ringtone type: {}", ringtoneType);

            // Check audio manager and volume first
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (audioManager == null) {
                LOG.warn("AudioManager is null");
                return false;
            }

            // Check if volume is not zero
            int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION);
            int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_NOTIFICATION);
            LOG.debug("Notification volume: {}/{}", currentVolume, maxVolume);

            if (currentVolume == 0) {
                LOG.warn("Notification volume is 0");
                return false;
            }

            // Try to get default ringtone URI
            android.net.Uri ringtoneUri = android.media.RingtoneManager.getDefaultUri(ringtoneType);
            LOG.debug("Default ringtone URI: {}", ringtoneUri);

            if (ringtoneUri == null) {
                LOG.warn("Default ringtone URI is null, trying alternative method");
                // Try alternative method
                android.media.RingtoneManager ringtoneManager = new android.media.RingtoneManager(context);
                ringtoneManager.setType(ringtoneType);
                android.database.Cursor cursor = ringtoneManager.getCursor();
                if (cursor != null && cursor.moveToFirst()) {
                    ringtoneUri = ringtoneManager.getRingtoneUri(0);
                    cursor.close();
                }
            }

            if (ringtoneUri != null) {
                android.media.Ringtone ringtone = android.media.RingtoneManager.getRingtone(context, ringtoneUri);
                if (ringtone != null) {
                    LOG.debug("Successfully created Ringtone object");

                    // Set audio stream type for older Android versions
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                        ringtone.setStreamType(AudioManager.STREAM_NOTIFICATION);
                    } else {
                        // Set audio attributes for newer versions
                        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .build();
                        ringtone.setAudioAttributes(audioAttributes);
                    }

                    ringtone.play();
                    LOG.info("Started playing system ringtone type: {}", ringtoneType);

                    // Stop ringtone after a reasonable duration
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        try {
                            if (ringtone.isPlaying()) {
                                ringtone.stop();
                                LOG.debug("Stopped system ringtone");
                            }
                        } catch (Exception e) {
                            LOG.warn("Error stopping ringtone", e);
                        }
                    }, 1000); // Stop after 1 second

                    return true; // Success
                } else {
                    LOG.warn("Failed to create Ringtone object");
                    return false;
                }
            } else {
                LOG.warn("No ringtone URI available");
                return false;
            }
        } catch (Exception e) {
            LOG.error("Failed to play system ringtone type: {}", ringtoneType, e);
            return false;
        }
    }

    /**
     * Alternative method to play system sound using MediaPlayer
     */
    private boolean playSystemSoundWithMediaPlayer(int ringtoneType) {
        try {
            LOG.debug("Trying MediaPlayer method for system sound type: {}", ringtoneType);

            // Get default URI for the ringtone type
            android.net.Uri soundUri = android.media.RingtoneManager.getDefaultUri(ringtoneType);
            if (soundUri == null) {
                LOG.warn("No default URI for ringtone type: {}", ringtoneType);
                return false;
            }

            MediaPlayer mediaPlayer = new MediaPlayer();
            mediaPlayer.setDataSource(context, soundUri);

            // Set audio attributes
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .build();
                mediaPlayer.setAudioAttributes(audioAttributes);
            } else {
                mediaPlayer.setAudioStreamType(AudioManager.STREAM_NOTIFICATION);
            }

            // Set volume
            float volume = getAudioVolume();
            mediaPlayer.setVolume(volume, volume);

            // Set completion listener for cleanup
            mediaPlayer.setOnCompletionListener(mp -> {
                try {
                    mp.release();
                    LOG.debug("MediaPlayer released after completion");
                } catch (Exception e) {
                    LOG.warn("Error releasing MediaPlayer", e);
                }
            });

            // Set error listener
            mediaPlayer.setOnErrorListener((mp, what, extra) -> {
                LOG.error("MediaPlayer error: what={}, extra={}", what, extra);
                try {
                    mp.release();
                } catch (Exception e) {
                    LOG.warn("Error releasing MediaPlayer after error", e);
                }
                return true;
            });

            mediaPlayer.prepare();
            mediaPlayer.start();

            // Auto-stop after 1 second to prevent long playback
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                try {
                    if (mediaPlayer.isPlaying()) {
                        mediaPlayer.stop();
                        LOG.debug("Stopped MediaPlayer after timeout");
                    }
                    mediaPlayer.release();
                } catch (Exception e) {
                    LOG.warn("Error stopping/releasing MediaPlayer", e);
                }
            }, 1000);

            LOG.info("Successfully started MediaPlayer for system sound type: {}", ringtoneType);
            return true;

        } catch (Exception e) {
            LOG.error("Failed to play system sound with MediaPlayer, type: {}", ringtoneType, e);
            return false;
        }
    }

    /**
     * Play tone using ToneGenerator with improved reliability
     */
    private void playToneGenerator(int toneType, int durationMs) {
        try {
            LOG.debug("Attempting to play tone: {} for {}ms", toneType, durationMs);

            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (audioManager == null) {
                LOG.warn("AudioManager is null, cannot play tone");
                return;
            }

            // Check notification volume
            int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION);
            int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_NOTIFICATION);
            LOG.debug("Notification volume for tone: {}/{}", currentVolume, maxVolume);

            if (currentVolume == 0) {
                LOG.warn("Notification volume is 0, cannot play tone");
                return;
            }

            // Use a reasonable volume level (50-80% of max)
            int toneVolume = Math.max(50, Math.min(80, (int) (getAudioVolume() * 100)));

            android.media.ToneGenerator toneGenerator = new android.media.ToneGenerator(
                AudioManager.STREAM_NOTIFICATION, toneVolume);

            boolean success = toneGenerator.startTone(toneType, durationMs);
            LOG.debug("ToneGenerator.startTone() returned: {}", success);

            if (success) {
                LOG.info("Successfully started tone: {} for {}ms at volume: {}", toneType, durationMs, toneVolume);
            } else {
                LOG.warn("ToneGenerator.startTone() failed for tone: {}", toneType);
            }

            // Clean up after tone finishes
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                try {
                    toneGenerator.release();
                    LOG.debug("Released ToneGenerator");
                } catch (Exception e) {
                    LOG.warn("Error releasing ToneGenerator", e);
                }
            }, durationMs + 200); // Add buffer time

        } catch (Exception e) {
            LOG.error("Failed to play tone: {} for {}ms", toneType, durationMs, e);
        }
    }
    
    /**
     * Play custom audio file
     */
    private void playCustomAudio() {
        String customAudioPath = preferenceHelper.getString(PreferenceNames.AUDIO_FEEDBACK_CUSTOM_PATH, "");
        
        if (customAudioPath.isEmpty()) {
            LOG.warn("Custom audio path is empty, falling back to default sound");
            playBuiltInAudio(AudioFeedbackType.TONE_BEEP);
            return;
        }
        
        try {
            if (mediaPlayer != null) {
                mediaPlayer.release();
            }
            
            mediaPlayer = new MediaPlayer();
            
            if (customAudioPath.startsWith("content://")) {
                // Content URI (from file picker)
                mediaPlayer.setDataSource(context, Uri.parse(customAudioPath));
            } else {
                // File path
                File audioFile = new File(customAudioPath);
                if (!audioFile.exists()) {
                    LOG.warn("Custom audio file not found: {}", customAudioPath);
                    playBuiltInAudio(AudioFeedbackType.TONE_BEEP);
                    return;
                }
                mediaPlayer.setDataSource(customAudioPath);
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_ASSISTANCE_SONIFICATION)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .build();
                mediaPlayer.setAudioAttributes(audioAttributes);
            } else {
                mediaPlayer.setAudioStreamType(AudioManager.STREAM_NOTIFICATION);
            }
            
            float volume = getAudioVolume();
            mediaPlayer.setVolume(volume, volume);
            
            mediaPlayer.setOnCompletionListener(mp -> {
                mp.release();
                mediaPlayer = null;
            });
            
            mediaPlayer.setOnErrorListener((mp, what, extra) -> {
                LOG.error("MediaPlayer error: what={}, extra={}", what, extra);
                mp.release();
                mediaPlayer = null;
                return true;
            });
            
            mediaPlayer.prepare();
            mediaPlayer.start();
            
            LOG.debug("Playing custom audio: {} at volume: {}", customAudioPath, volume);
            
        } catch (Exception e) {
            LOG.error("Failed to play custom audio: {}", customAudioPath, e);
            if (mediaPlayer != null) {
                mediaPlayer.release();
                mediaPlayer = null;
            }
            // Fallback to built-in sound
            playBuiltInAudio(AudioFeedbackType.TONE_BEEP);
        }
    }
    
    /**
     * Get audio volume based on system settings
     */
    private float getAudioVolume() {
        try {
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (audioManager != null) {
                int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION);
                int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_NOTIFICATION);
                return maxVolume > 0 ? (float) currentVolume / maxVolume : 0.5f;
            }
        } catch (Exception e) {
            LOG.warn("Failed to get system volume, using default", e);
        }
        return 0.5f; // Default volume
    }
    
    /**
     * Test audio feedback
     */
    public void testAudioFeedback() {
        AudioFeedbackType feedbackType = getAudioFeedbackType();
        
        if (feedbackType == AudioFeedbackType.CUSTOM) {
            playCustomAudio();
        } else {
            playBuiltInAudio(feedbackType);
        }
        
        LOG.info("Testing audio feedback: {}", feedbackType.getDisplayName());
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        try {
            if (soundPool != null) {
                soundPool.release();
                soundPool = null;
            }
            
            if (mediaPlayer != null) {
                mediaPlayer.release();
                mediaPlayer = null;
            }
            
            soundIds.clear();
            isInitialized = false;
            LOG.debug("AudioFeedbackManager cleaned up");
            
        } catch (Exception e) {
            LOG.error("Error during AudioFeedbackManager cleanup", e);
        }
    }
}
