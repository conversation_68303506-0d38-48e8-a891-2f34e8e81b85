/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Handler;
import android.os.Looper;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 统一音频处理器 - 只用于语音识别，不进行录音
 * 删除了录音功能，只保留音频数据流给语音识别
 */
public class UnifiedAudioProcessor {
    private static final Logger LOG = Logs.of(UnifiedAudioProcessor.class);
    
    // 音频参数配置 - 针对语音识别优化
    private static final int SAMPLE_RATE = 16000; // 16kHz适合语音识别
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private static final int AUDIO_SOURCE = MediaRecorder.AudioSource.VOICE_RECOGNITION;
    
    // 缓冲区大小
    private static final int BUFFER_SIZE_MULTIPLIER = 4; // 增大缓冲区以提高稳定性
    
    // 音频处理超时设置（5分钟）
    private static final long PROCESSING_TIMEOUT_MS = 5 * 60 * 1000;
    
    private Context context;
    private PreferenceHelper preferenceHelper;
    private Handler mainHandler;
    
    // 音频录制相关
    private AudioRecord audioRecord;
    private Thread processingThread;
    private AtomicBoolean isProcessing = new AtomicBoolean(false);
    private AtomicBoolean shouldStop = new AtomicBoolean(false);
    private int bufferSize;
    
    // 语音识别相关
    private AudioDataListener audioDataListener;
    private Runnable timeoutRunnable;
    
    // 监听器接口
    public interface AudioDataListener {
        /**
         * 接收音频数据用于语音识别
         * @param audioData PCM音频数据
         * @param length 数据长度
         */
        void onAudioData(byte[] audioData, int length);
        
        /**
         * 音频处理开始
         */
        void onAudioStarted();
        
        /**
         * 音频处理结束
         */
        void onAudioStopped();
        
        /**
         * 音频处理错误
         * @param error 错误信息
         */
        void onAudioError(String error);
    }
    
    /**
     * 构造函数
     */
    public UnifiedAudioProcessor(Context context) {
        this.context = context;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // 计算缓冲区大小
        int minBufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
        this.bufferSize = minBufferSize * BUFFER_SIZE_MULTIPLIER;
        
        LOG.debug("UnifiedAudioProcessor initialized - buffer size: {} bytes (speech recognition only)", bufferSize);
    }
    
    /**
     * 设置音频数据监听器（用于语音识别）
     */
    public void setAudioDataListener(AudioDataListener listener) {
        this.audioDataListener = listener;
    }
    
    /**
     * 开始音频处理（只用于语音识别）
     * @param recordingFilePath 忽略此参数，不进行录音
     * @return 是否成功启动
     */
    public boolean startProcessing(String recordingFilePath) {
        if (isProcessing.get()) {
            LOG.warn("Audio processing already in progress");
            return false;
        }
        
        try {
            // 检查权限
            if (!hasRecordAudioPermission()) {
                notifyError("没有录音权限");
                return false;
            }
            
            // 初始化AudioRecord
            if (!initializeAudioRecord()) {
                return false;
            }
            
            // 启动处理线程
            shouldStop.set(false);
            processingThread = new Thread(this::audioProcessingLoop, "UnifiedAudioProcessor");
            processingThread.start();
            
            // 启动AudioRecord
            audioRecord.startRecording();
            isProcessing.set(true);
            
            // 设置超时保护
            setupTimeout();
            
            // 通知监听器
            if (audioDataListener != null) {
                audioDataListener.onAudioStarted();
            }
            
            LOG.info("Unified audio processing started successfully (speech recognition only)");
            return true;
            
        } catch (Exception e) {
            LOG.error("Failed to start audio processing", e);
            cleanup();
            notifyError("启动音频处理失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 停止音频处理
     * @param finalFileName 忽略此参数，不进行录音
     * @return 返回null，因为不进行录音
     */
    public String stopProcessing(String finalFileName) {
        if (!isProcessing.get()) {
            LOG.warn("Audio processing is not running");
            return null;
        }
        
        LOG.debug("Stopping unified audio processing");
        
        // 停止处理
        shouldStop.set(true);
        isProcessing.set(false);
        
        // 清除超时
        clearTimeout();
        
        // 停止AudioRecord
        try {
            if (audioRecord != null && audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                audioRecord.stop();
            }
        } catch (Exception e) {
            LOG.warn("Error stopping AudioRecord", e);
        }
        
        // 等待处理线程结束
        if (processingThread != null) {
            try {
                processingThread.join(2000); // 最多等待2秒
            } catch (InterruptedException e) {
                LOG.warn("Processing thread join interrupted", e);
            }
        }
        
        // 清理资源
        cleanup();
        
        // 通知监听器
        if (audioDataListener != null) {
            audioDataListener.onAudioStopped();
        }
        
        LOG.info("Unified audio processing stopped (no recording file created)");
        return null; // 不返回录音文件路径，因为没有录音
    }
    
    /**
     * 取消音频处理
     */
    public void cancelProcessing() {
        if (!isProcessing.get()) {
            return;
        }
        
        LOG.debug("Cancelling unified audio processing");
        
        // 停止处理
        shouldStop.set(true);
        isProcessing.set(false);
        
        // 清除超时
        clearTimeout();
        
        // 停止AudioRecord
        try {
            if (audioRecord != null && audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                audioRecord.stop();
            }
        } catch (Exception e) {
            LOG.warn("Error stopping AudioRecord during cancel", e);
        }
        
        // 等待处理线程结束
        if (processingThread != null) {
            try {
                processingThread.join(1000);
            } catch (InterruptedException e) {
                LOG.warn("Processing thread join interrupted during cancel", e);
            }
        }
        
        // 清理资源
        cleanup();
        
        // 通知监听器
        if (audioDataListener != null) {
            audioDataListener.onAudioStopped();
        }
        
        LOG.info("Unified audio processing cancelled");
    }
    
    /**
     * 检查是否正在处理音频
     */
    public boolean isProcessing() {
        return isProcessing.get();
    }
    
    /**
     * 检查录音权限
     */
    public boolean hasRecordAudioPermission() {
        return androidx.core.content.ContextCompat.checkSelfPermission(context,
                android.Manifest.permission.RECORD_AUDIO) == android.content.pm.PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 初始化AudioRecord
     */
    private boolean initializeAudioRecord() {
        try {
            audioRecord = new AudioRecord(
                AUDIO_SOURCE,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            );

            if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                LOG.error("AudioRecord initialization failed");
                return false;
            }

            LOG.debug("AudioRecord initialized successfully");
            return true;

        } catch (Exception e) {
            LOG.error("Failed to initialize AudioRecord", e);
            return false;
        }
    }

    /**
     * 音频处理主循环
     */
    private void audioProcessingLoop() {
        LOG.debug("Audio processing loop started (speech recognition only)");

        byte[] buffer = new byte[bufferSize];

        while (!shouldStop.get() && isProcessing.get()) {
            try {
                int bytesRead = audioRecord.read(buffer, 0, buffer.length);

                if (bytesRead > 0) {
                    // 发送给语音识别监听器
                    if (audioDataListener != null) {
                        audioDataListener.onAudioData(buffer, bytesRead);
                    }

                } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                    LOG.error("AudioRecord read error: INVALID_OPERATION");
                    break;
                } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                    LOG.error("AudioRecord read error: BAD_VALUE");
                    break;
                }

            } catch (Exception e) {
                LOG.error("Error in audio processing loop", e);
                break;
            }
        }

        LOG.debug("Audio processing loop ended");
    }

    /**
     * 设置超时保护
     */
    private void setupTimeout() {
        clearTimeout();

        timeoutRunnable = () -> {
            LOG.warn("Audio processing timeout reached, stopping automatically");
            if (isProcessing.get()) {
                cancelProcessing();
                notifyError("音频处理超时，已自动停止");
            }
        };

        mainHandler.postDelayed(timeoutRunnable, PROCESSING_TIMEOUT_MS);
        LOG.debug("Audio processing timeout set for {} minutes", PROCESSING_TIMEOUT_MS / (60 * 1000));
    }

    /**
     * 清除超时保护
     */
    private void clearTimeout() {
        if (timeoutRunnable != null && mainHandler != null) {
            mainHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
        }
    }

    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            // 停止AudioRecord
            if (audioRecord != null) {
                if (audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                    audioRecord.stop();
                }
                audioRecord.release();
                audioRecord = null;
            }

        } catch (Exception e) {
            LOG.warn("Error during cleanup", e);
        }

        isProcessing.set(false);
        shouldStop.set(false);
        processingThread = null;

        clearTimeout();
    }

    /**
     * 通知错误
     */
    private void notifyError(String error) {
        if (audioDataListener != null) {
            audioDataListener.onAudioError(error);
        }
    }

    /**
     * 获取音频参数信息
     */
    public static String getAudioInfo() {
        return String.format("采样率: %d Hz, 声道: 单声道, 位深: 16位, 音频源: VOICE_RECOGNITION (仅语音识别)", SAMPLE_RATE);
    }

    /**
     * 检查设备是否支持音频录制
     */
    public static boolean isAudioRecordingSupported(Context context) {
        try {
            int minBufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
            if (minBufferSize == AudioRecord.ERROR_BAD_VALUE || minBufferSize == AudioRecord.ERROR) {
                return false;
            }

            AudioRecord testRecord = new AudioRecord(
                AUDIO_SOURCE,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                minBufferSize
            );

            boolean supported = testRecord.getState() == AudioRecord.STATE_INITIALIZED;
            testRecord.release();

            return supported;

        } catch (Exception e) {
            return false;
        }
    }
}
