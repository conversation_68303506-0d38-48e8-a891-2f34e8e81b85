/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.external;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.view.InputDevice;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;

import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.PreferenceNames;
import com.mendhak.gpslogger.common.events.ExternalControlEvents;
import com.mendhak.gpslogger.common.events.ExternalControlEvents.ButtonAction;
import com.mendhak.gpslogger.common.slf4j.Logs;

import de.greenrobot.event.EventBus;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Manages steering wheel button events from car infotainment systems
 * Handles both Bluetooth HID and AVRCP protocol button events
 * Implements unified device detection interface for consistency
 */
public class SteeringWheelButtonManager implements DeviceDetectionInterface {
    private static final Logger LOG = Logs.of(SteeringWheelButtonManager.class);
    
    private final Context context;
    private final PreferenceHelper preferenceHelper;
    private final ButtonActionMapper actionMapper;
    
    private boolean isListening = false;
    private boolean isEnabled = false;
    
    // Steering wheel button types - simplified to 4 basic buttons
    public enum SteeringWheelButton {
        VOLUME_UP("音量+", KeyEvent.KEYCODE_VOLUME_UP),
        VOLUME_DOWN("音量-", KeyEvent.KEYCODE_VOLUME_DOWN),
        MEDIA_NEXT("媒体下一首", KeyEvent.KEYCODE_MEDIA_NEXT),
        MEDIA_PREVIOUS("媒体上一首", KeyEvent.KEYCODE_MEDIA_PREVIOUS);
        
        private final String displayName;
        private final int keyCode;
        
        SteeringWheelButton(String displayName, int keyCode) {
            this.displayName = displayName;
            this.keyCode = keyCode;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public int getKeyCode() {
            return keyCode;
        }
        
        public static SteeringWheelButton fromKeyCode(int keyCode) {
            for (SteeringWheelButton button : values()) {
                if (button.keyCode == keyCode) {
                    return button;
                }
            }
            return null;
        }
    }
    
    // Button press tracking for gesture detection (single and double press only)
    private final Map<SteeringWheelButton, Long> lastPressTime = new HashMap<>();
    private final Map<SteeringWheelButton, Integer> pressCount = new HashMap<>();
    private static final long DOUBLE_PRESS_TIMEOUT = 500; // 500ms for double press
    
    // Bluetooth device tracking
    private BluetoothDevice connectedCarDevice = null;

    // Detection mode for unified interface
    private DetectionMode detectionMode = DetectionMode.NORMAL;
    
    public SteeringWheelButtonManager(Context context) {
        this.context = context;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.actionMapper = new ButtonActionMapper(context);

        // Initialize default mappings if none exist
        initializeDefaultMappings();

        detectCarBluetoothDevice();
    }

    /**
     * Initialize default button mappings if none exist
     */
    private void initializeDefaultMappings() {
        // Check if any mappings already exist
        boolean hasExistingMappings = false;
        for (SteeringWheelButton button : SteeringWheelButton.values()) {
            for (GestureType gesture : GestureType.values()) {
                ButtonAction action = getButtonAction(button, gesture);
                if (action != ButtonAction.NONE) {
                    hasExistingMappings = true;
                    break;
                }
            }
            if (hasExistingMappings) break;
        }

        // If no mappings exist, set up defaults for 4 basic buttons
        if (!hasExistingMappings) {
            LOG.info("Initializing default steering wheel button mappings for 4 basic buttons");

            // Volume buttons - commonly used for GPS control
            setButtonAction(SteeringWheelButton.VOLUME_UP, GestureType.SINGLE_PRESS, ButtonAction.START_STOP_LOGGING);
            setButtonAction(SteeringWheelButton.VOLUME_UP, GestureType.DOUBLE_PRESS, ButtonAction.LOG_SINGLE_POINT);

            setButtonAction(SteeringWheelButton.VOLUME_DOWN, GestureType.SINGLE_PRESS, ButtonAction.ADD_WAYPOINT);
            setButtonAction(SteeringWheelButton.VOLUME_DOWN, GestureType.DOUBLE_PRESS, ButtonAction.VOICE_INPUT);

            // Media buttons - for additional functions
            setButtonAction(SteeringWheelButton.MEDIA_NEXT, GestureType.SINGLE_PRESS, ButtonAction.QUICK_VOICE_INPUT);
            setButtonAction(SteeringWheelButton.MEDIA_NEXT, GestureType.DOUBLE_PRESS, ButtonAction.ANNOTATION_BUTTON_1);

            setButtonAction(SteeringWheelButton.MEDIA_PREVIOUS, GestureType.SINGLE_PRESS, ButtonAction.QUICK_TEXT_INPUT);
            setButtonAction(SteeringWheelButton.MEDIA_PREVIOUS, GestureType.DOUBLE_PRESS, ButtonAction.ANNOTATION_BUTTON_2);

            LOG.info("Default steering wheel button mappings initialized for 4 buttons");
        }
    }
    
    /**
     * Enable or disable steering wheel button monitoring
     */
    public void setEnabled(boolean enabled) {
        LOG.info("Steering wheel button monitoring {}", enabled ? "enabled" : "disabled");

        try {
            if (enabled) {
                // First set the preference
                preferenceHelper.setBoolean(PreferenceNames.STEERING_WHEEL_ENABLED, enabled);
                // Then try to start listening
                startListening();

                // Check if listening actually started
                if (!isListening) {
                    LOG.warn("Failed to start steering wheel listening, disabling feature");
                    preferenceHelper.setBoolean(PreferenceNames.STEERING_WHEEL_ENABLED, false);
                    this.isEnabled = false;
                } else {
                    this.isEnabled = enabled;
                }
            } else {
                // First stop listening
                stopListening();
                // Then set the preference
                preferenceHelper.setBoolean(PreferenceNames.STEERING_WHEEL_ENABLED, enabled);
                this.isEnabled = enabled;
            }
        } catch (Exception e) {
            LOG.error("Error setting steering wheel button monitoring to {}", enabled, e);
            preferenceHelper.setBoolean(PreferenceNames.STEERING_WHEEL_ENABLED, false);
            this.isEnabled = false;
            stopListening();
            // Don't re-throw exception - just log and continue
        }
    }
    
    /**
     * Check if steering wheel button monitoring is enabled
     */
    public boolean isEnabled() {
        return preferenceHelper.getBoolean(PreferenceNames.STEERING_WHEEL_ENABLED, false);
    }
    
    /**
     * Start listening for steering wheel button events
     */
    public void startListening() {
        if (isListening) {
            LOG.debug("Already listening to steering wheel events");
            return;
        }

        // First try to register with full functionality including media button support
        boolean registeredWithMediaButton = tryRegisterWithMediaButton();

        // If media button registration failed, try without it
        if (!registeredWithMediaButton) {
            boolean registeredFallback = tryRegisterFallback();
            if (!registeredFallback) {
                LOG.error("Failed to register steering wheel button listener with any method");
                isListening = false;
                return;
            }
        }

        // Detect car device after successful registration
        detectCarBluetoothDevice();
        LOG.info("Steering wheel button manager started successfully - listening: {}, car device: {}",
                isListening, connectedCarDevice != null ? connectedCarDevice.getName() : "none");
    }

    /**
     * Try to register receiver with full media button support
     */
    private boolean tryRegisterWithMediaButton() {
        try {
            IntentFilter bluetoothFilter = new IntentFilter();
            bluetoothFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
            bluetoothFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
            bluetoothFilter.addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY);
            bluetoothFilter.addAction(Intent.ACTION_MEDIA_BUTTON);

            context.registerReceiver(bluetoothReceiver, bluetoothFilter);
            isListening = true;
            LOG.info("Started listening to steering wheel events with full media button support");
            return true;

        } catch (SecurityException e) {
            LOG.warn("Security exception when registering with media button support: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            LOG.error("Unexpected error when registering with media button support", e);
            return false;
        }
    }

    /**
     * Try to register receiver without media button support (fallback)
     */
    private boolean tryRegisterFallback() {
        try {
            IntentFilter fallbackFilter = new IntentFilter();
            fallbackFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
            fallbackFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
            fallbackFilter.addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY);

            context.registerReceiver(bluetoothReceiver, fallbackFilter);
            isListening = true;
            LOG.info("Started listening to steering wheel events (fallback mode - no media button support)");
            return true;

        } catch (Exception e) {
            LOG.error("Failed to register steering wheel listener even in fallback mode", e);
            return false;
        }
    }
    
    /**
     * Stop listening for steering wheel button events
     */
    public void stopListening() {
        if (!isListening) {
            return;
        }
        
        try {
            context.unregisterReceiver(bluetoothReceiver);
            isListening = false;
            connectedCarDevice = null;
            LOG.info("Stopped listening to steering wheel button events");
        } catch (Exception e) {
            LOG.warn("Error stopping steering wheel button listener", e);
        }
    }
    
    /**
     * Handle key events that might be from steering wheel
     */
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (!isEnabled() || !isListening) {
            return false;
        }
        
        SteeringWheelButton button = SteeringWheelButton.fromKeyCode(keyCode);
        if (button == null) {
            return false;
        }
        
        // Check if this event is from a car device
        if (!isFromCarDevice(event)) {
            LOG.debug("Key event not from car device, ignoring");
            return false;
        }
        
        LOG.info("Steering wheel button pressed: {} (keyCode: {})", button.getDisplayName(), keyCode);
        
        // Handle button press with gesture detection
        return handleButtonPress(button, event);
    }
    
    /**
     * Handle button press with simplified gesture detection (single and double press only)
     */
    private boolean handleButtonPress(SteeringWheelButton button, KeyEvent event) {
        long currentTime = System.currentTimeMillis();
        Long lastPress = lastPressTime.get(button);
        Integer currentCount = pressCount.getOrDefault(button, 0);

        if (lastPress != null && (currentTime - lastPress) < DOUBLE_PRESS_TIMEOUT) {
            // This is a potential double press
            pressCount.put(button, currentCount + 1);
        } else {
            // Reset count for new press sequence
            pressCount.put(button, 1);
        }

        lastPressTime.put(button, currentTime);

        // Schedule gesture processing (no long press detection)
        scheduleGestureProcessing(button);
        return true; // Consume the event
    }
    
    /**
     * Schedule gesture processing after timeout
     */
    private void scheduleGestureProcessing(SteeringWheelButton button) {
        // Use a simple delay mechanism
        new Thread(() -> {
            try {
                Thread.sleep(DOUBLE_PRESS_TIMEOUT + 50); // Wait a bit longer than timeout
                processGesture(button);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    /**
     * Process the final gesture after timeout
     */
    private void processGesture(SteeringWheelButton button) {
        Integer count = pressCount.get(button);
        if (count == null) return;
        
        GestureType gestureType;
        switch (count) {
            case 1:
                gestureType = GestureType.SINGLE_PRESS;
                break;
            case 2:
                gestureType = GestureType.DOUBLE_PRESS;
                break;
            default:
                gestureType = GestureType.SINGLE_PRESS;
                break;
        }
        
        executeButtonAction(button, gestureType);
        pressCount.remove(button);
    }
    
    /**
     * Execute the mapped action for a button and gesture
     */
    private boolean executeButtonAction(SteeringWheelButton button, GestureType gestureType) {
        ButtonAction action = getButtonAction(button, gestureType);
        if (action == ButtonAction.NONE) {
            LOG.debug("No action mapped for {} {}", button.getDisplayName(), gestureType);
            return false;
        }
        
        String source = "方向盘-" + button.getDisplayName() + "-" + gestureType.getDisplayName();
        LOG.info("Executing steering wheel action: {} from {}", action, source);
        
        try {
            actionMapper.executeAction(action, source);
            
            // Post event for other components
            EventBus.getDefault().post(new ExternalControlEvents.SteeringWheelButtonPressed(
                    button, action, gestureType));
            
            return true;
        } catch (Exception e) {
            LOG.error("Failed to execute steering wheel action: {}", action, e);
            return false;
        }
    }
    
    // Gesture types for steering wheel buttons - simplified to single and double press only
    public enum GestureType {
        SINGLE_PRESS("单击"),
        DOUBLE_PRESS("双击");
        
        private final String displayName;
        
        GestureType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * Get the mapped action for a button and gesture
     */
    private ButtonAction getButtonAction(SteeringWheelButton button, GestureType gestureType) {
        String prefKey = getPreferenceKey(button, gestureType);
        String actionStr = preferenceHelper.getString(prefKey, ButtonAction.NONE.name());
        return ButtonAction.fromString(actionStr);
    }
    
    /**
     * Set the mapped action for a button and gesture
     */
    public void setButtonAction(SteeringWheelButton button, GestureType gestureType, ButtonAction action) {
        String prefKey = getPreferenceKey(button, gestureType);
        preferenceHelper.setString(prefKey, action.name());
        LOG.info("Set {} {} action to: {}", button.getDisplayName(), gestureType.getDisplayName(), action);
    }
    
    /**
     * Get preference key for button and gesture combination
     */
    private String getPreferenceKey(SteeringWheelButton button, GestureType gestureType) {
        return "steering_wheel_" + button.name().toLowerCase() + "_" + gestureType.name().toLowerCase();
    }

    /**
     * Get all available button and gesture combinations
     */
    public java.util.List<ButtonGestureCombination> getAllButtonGestureCombinations() {
        java.util.List<ButtonGestureCombination> combinations = new java.util.ArrayList<>();

        for (SteeringWheelButton button : SteeringWheelButton.values()) {
            for (GestureType gesture : GestureType.values()) {
                combinations.add(new ButtonGestureCombination(button, gesture));
            }
        }

        return combinations;
    }

    /**
     * Get all current mappings
     */
    public Map<ButtonGestureCombination, ButtonAction> getAllMappings() {
        Map<ButtonGestureCombination, ButtonAction> mappings = new HashMap<>();

        for (SteeringWheelButton button : SteeringWheelButton.values()) {
            for (GestureType gesture : GestureType.values()) {
                ButtonAction action = getButtonAction(button, gesture);
                if (action != ButtonAction.NONE) {
                    mappings.put(new ButtonGestureCombination(button, gesture), action);
                }
            }
        }

        return mappings;
    }

    /**
     * Clear all mappings
     */
    public void clearAllMappings() {
        for (SteeringWheelButton button : SteeringWheelButton.values()) {
            for (GestureType gesture : GestureType.values()) {
                setButtonAction(button, gesture, ButtonAction.NONE);
            }
        }
        LOG.info("Cleared all steering wheel button mappings");
    }

    /**
     * Button and gesture combination holder
     */
    public static class ButtonGestureCombination {
        public final SteeringWheelButton button;
        public final GestureType gesture;

        public ButtonGestureCombination(SteeringWheelButton button, GestureType gesture) {
            this.button = button;
            this.gesture = gesture;
        }

        public String getDisplayName() {
            return button.getDisplayName() + " - " + gesture.getDisplayName();
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            ButtonGestureCombination that = (ButtonGestureCombination) obj;
            return button == that.button && gesture == that.gesture;
        }

        @Override
        public int hashCode() {
            return java.util.Objects.hash(button, gesture);
        }
    }
    
    /**
     * Check if the key event is from a car device (legacy method)
     * Now uses the unified detection interface
     */
    private boolean isFromCarDevice(KeyEvent event) {
        DetectionMode currentMode = getDetectionMode();
        DetectionResult result = shouldProcessEvent(event, currentMode);

        LOG.debug("Car device check using unified interface - result: {}, reason: {}",
                 result.shouldProcess, result.reason);

        return result.shouldProcess;
    }

    /**
     * Enhanced check for external input devices
     */
    private boolean isFromExternalInputDevice(KeyEvent event) {
        int source = event.getSource();
        int deviceId = event.getDeviceId();

        // Check various external device sources
        boolean isBluetoothSource = (source & InputDevice.SOURCE_BLUETOOTH_STYLUS) != 0;
        boolean isHdmiSource = (source & InputDevice.SOURCE_HDMI) != 0;
        boolean isNotVirtualKeyboard = deviceId != KeyCharacterMap.VIRTUAL_KEYBOARD;
        boolean isExternalDeviceId = deviceId > 0; // Virtual keyboard usually has deviceId 0 or -1

        return isBluetoothSource || isHdmiSource || (isNotVirtualKeyboard && isExternalDeviceId);
    }

    /**
     * Check if the key event is from our known car device
     */
    private boolean isFromKnownCarDevice(KeyEvent event) {
        if (connectedCarDevice == null) {
            return false;
        }

        try {
            // Try to get the input device and match it to our car device
            InputDevice inputDevice = InputDevice.getDevice(event.getDeviceId());
            if (inputDevice != null) {
                String deviceName = inputDevice.getName();
                if (deviceName != null && connectedCarDevice.getName() != null) {
                    // Check if the input device name contains the car device name or vice versa
                    String carName = connectedCarDevice.getName().toLowerCase();
                    String inputName = deviceName.toLowerCase();

                    boolean nameMatch = carName.contains(inputName) || inputName.contains(carName);
                    LOG.debug("Device name match check: car='{}', input='{}', match={}",
                             carName, inputName, nameMatch);
                    return nameMatch;
                }
            }
        } catch (Exception e) {
            LOG.debug("Could not check input device for car device match", e);
        }

        return false;
    }
    
    /**
     * Check if the key code is from supported steering wheel buttons
     * Only supports 4 basic buttons: Volume+/-, Media Next/Previous
     */
    private boolean isCarKeyCode(int keyCode) {
        return keyCode == KeyEvent.KEYCODE_VOLUME_UP ||
               keyCode == KeyEvent.KEYCODE_VOLUME_DOWN ||
               keyCode == KeyEvent.KEYCODE_MEDIA_NEXT ||
               keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS;
    }
    
    /**
     * Detect connected car Bluetooth device
     */
    private void detectCarBluetoothDevice() {
        try {
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
                LOG.debug("Bluetooth not available or not enabled");
                return;
            }

            // Check both paired and connected devices
            Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
            LOG.debug("Checking {} paired Bluetooth devices for car devices", pairedDevices.size());

            for (BluetoothDevice device : pairedDevices) {
                LOG.debug("Checking device: {} ({})", device.getName(), device.getAddress());
                if (isCarDevice(device)) {
                    // Check if device is actually connected
                    if (isDeviceConnected(device)) {
                        connectedCarDevice = device;
                        LOG.info("Detected connected car device: {} ({})", device.getName(), device.getAddress());
                        return;
                    } else {
                        LOG.debug("Car device {} is paired but not connected", device.getName());
                    }
                }
            }

            if (connectedCarDevice == null) {
                LOG.debug("No connected car device found among {} paired devices", pairedDevices.size());
            }
        } catch (SecurityException e) {
            LOG.warn("No permission to access Bluetooth devices", e);
        } catch (Exception e) {
            LOG.warn("Error detecting car Bluetooth device", e);
        }
    }

    /**
     * Check if a Bluetooth device is currently connected
     */
    private boolean isDeviceConnected(BluetoothDevice device) {
        try {
            // Use reflection to check connection state
            java.lang.reflect.Method isConnectedMethod = BluetoothDevice.class.getDeclaredMethod("isConnected");
            return (Boolean) isConnectedMethod.invoke(device);
        } catch (Exception e) {
            LOG.debug("Could not check connection state for device {}, assuming connected", device.getName());
            // If we can't check, assume it's connected if it's a car device
            return true;
        }
    }
    
    /**
     * Check if a Bluetooth device is likely a car device
     */
    private boolean isCarDevice(BluetoothDevice device) {
        if (device.getName() == null) return false;

        String name = device.getName().toLowerCase();

        // Check device class for automotive indicators
        boolean isAutomotiveClass = false;
        try {
            BluetoothClass bluetoothClass = device.getBluetoothClass();
            if (bluetoothClass != null) {
                int majorDeviceClass = bluetoothClass.getMajorDeviceClass();
                int deviceClass = bluetoothClass.getDeviceClass();

                // Check for automotive device classes
                // Note: BluetoothClass.Device.Major.AUTOMOTIVE doesn't exist in Android API
                // Use audio/video device classes that are common in cars
                isAutomotiveClass = deviceClass == BluetoothClass.Device.AUDIO_VIDEO_CAR_AUDIO ||
                                   deviceClass == BluetoothClass.Device.AUDIO_VIDEO_HANDSFREE ||
                                   majorDeviceClass == BluetoothClass.Device.Major.AUDIO_VIDEO;

                LOG.debug("Device {} class check: major=0x{}, device=0x{}, automotive={}",
                         name, Integer.toHexString(majorDeviceClass), Integer.toHexString(deviceClass), isAutomotiveClass);
            }
        } catch (Exception e) {
            LOG.debug("Could not check device class for {}", name);
        }

        // Car brand names - International brands
        boolean isCarBrand = name.contains("car") ||
               name.contains("auto") ||
               name.contains("vehicle") ||
               name.contains("bmw") ||
               name.contains("audi") ||
               name.contains("mercedes") ||
               name.contains("toyota") ||
               name.contains("honda") ||
               name.contains("ford") ||
               name.contains("volkswagen") ||
               name.contains("hyundai") ||
               name.contains("kia") ||
               name.contains("nissan") ||
               name.contains("mazda") ||
               name.contains("subaru") ||
               name.contains("volvo") ||
               name.contains("lexus") ||
               name.contains("acura") ||
               name.contains("infiniti") ||
               name.contains("cadillac") ||
               name.contains("buick") ||
               name.contains("chevrolet") ||
               name.contains("gmc") ||
               name.contains("lincoln") ||
               name.contains("jeep") ||
               name.contains("dodge") ||
               name.contains("chrysler") ||
               name.contains("ram") ||
               // Chinese car brands - 中国汽车品牌
               name.contains("changan") ||      // 长安汽车
               name.contains("长安") ||
               name.contains("geely") ||        // 吉利汽车
               name.contains("吉利") ||
               name.contains("byd") ||          // 比亚迪
               name.contains("比亚迪") ||
               name.contains("chery") ||        // 奇瑞汽车
               name.contains("奇瑞") ||
               name.contains("haval") ||        // 哈弗
               name.contains("哈弗") ||
               name.contains("greatwall") ||    // 长城汽车
               name.contains("长城") ||
               name.contains("hongqi") ||       // 红旗汽车
               name.contains("红旗") ||
               name.contains("faw") ||          // 一汽
               name.contains("一汽") ||
               name.contains("dongfeng") ||     // 东风汽车
               name.contains("东风") ||
               name.contains("saic") ||         // 上汽
               name.contains("上汽") ||
               name.contains("roewe") ||        // 荣威
               name.contains("荣威") ||
               name.contains("mg") ||           // 名爵
               name.contains("名爵") ||
               name.contains("xpeng") ||        // 小鹏汽车
               name.contains("小鹏") ||
               name.contains("nio") ||          // 蔚来汽车
               name.contains("蔚来") ||
               name.contains("li auto") ||      // 理想汽车
               name.contains("理想") ||
               name.contains("lynk") ||         // 领克
               name.contains("领克") ||
               name.contains("wey") ||          // WEY
               name.contains("tank") ||         // 坦克
               name.contains("ora") ||          // 欧拉
               name.contains("欧拉");

        // Android Auto / CarPlay indicators and car systems
        boolean isCarSystem = name.contains("android auto") ||
               name.contains("carplay") ||
               name.contains("android automotive") ||
               name.contains("car multimedia") ||
               name.contains("infotainment") ||
               name.contains("车机") ||           // Chinese: car machine
               name.contains("车载") ||           // Chinese: in-car
               name.contains("导航") ||           // Chinese: navigation
               name.contains("multimedia") ||
               name.contains("entertainment") ||
               name.contains("head unit") ||
               name.contains("stereo") ||
               name.contains("radio") ||
               // Common Android car system patterns
               name.contains("android") && (name.contains("car") || name.contains("auto") || name.contains("vehicle")) ||
               // Pattern for Chinese car systems
               name.matches(".*\\d{4}.*车机.*") ||  // Year + 车机
               name.matches(".*车机.*\\d{4}.*");

        // Generic car device patterns
        boolean isCarPattern = name.matches(".*\\b(car|auto|vehicle)\\b.*") ||
               name.matches(".*\\d{4}.*car.*") ||  // Year + car
               name.matches(".*car.*\\d{4}.*") ||  // car + Year
               // Chinese car patterns
               name.matches(".*\\b(车机|车载|导航)\\b.*") ||
               name.matches(".*\\d{4}.*(车机|车载).*") ||  // Year + Chinese car terms
               name.matches(".*(车机|车载).*\\d{4}.*") ||  // Chinese car terms + Year
               // Common automotive device naming patterns
               name.matches(".*\\b(bt|bluetooth).*(car|auto|vehicle).*") ||
               name.matches(".*(car|auto|vehicle).*(bt|bluetooth).*") ||
               name.matches(".*\\b(hfp|a2dp|avrcp).*") ||  // Bluetooth profiles common in cars
               // Pattern for model names that might be car-related
               name.matches(".*[A-Z]{2,}\\d{2,}.*") && (name.length() < 20); // Short alphanumeric codes typical of car systems

        boolean result = isCarBrand || isCarSystem || isCarPattern || isAutomotiveClass;

        // Special logging for Chinese car brands
        boolean isChineseBrand = name.contains("changan") || name.contains("长安") ||
                                name.contains("geely") || name.contains("吉利") ||
                                name.contains("byd") || name.contains("比亚迪");

        LOG.debug("Device {} car detection: brand={}, system={}, pattern={}, class={}, chinese={}, result={}",
                 device.getName(), isCarBrand, isCarSystem, isCarPattern, isAutomotiveClass, isChineseBrand, result);

        // Special handling for Changan (长安) vehicles
        if (name.contains("changan") || name.contains("长安")) {
            LOG.info("Detected Changan (长安) car device: {}", device.getName());
        }

        return result;
    }
    
    /**
     * Bluetooth event receiver
     */
    private final BroadcastReceiver bluetoothReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action == null) return;
            
            switch (action) {
                case BluetoothDevice.ACTION_ACL_CONNECTED:
                    BluetoothDevice connectedDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                    if (connectedDevice != null && isCarDevice(connectedDevice)) {
                        connectedCarDevice = connectedDevice;
                        LOG.info("Car device connected: {}", connectedDevice.getName());
                    }
                    break;
                    
                case BluetoothDevice.ACTION_ACL_DISCONNECTED:
                    BluetoothDevice disconnectedDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                    if (disconnectedDevice != null && disconnectedDevice.equals(connectedCarDevice)) {
                        connectedCarDevice = null;
                        LOG.info("Car device disconnected: {}", disconnectedDevice.getName());
                    }
                    break;
                    
                case Intent.ACTION_MEDIA_BUTTON:
                    KeyEvent keyEvent = intent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
                    if (keyEvent != null && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                        LOG.debug("Media button event received: {}", keyEvent.getKeyCode());
                        onKeyDown(keyEvent.getKeyCode(), keyEvent);
                    }
                    break;
            }
        }
    };
    
    /**
     * Get status summary for debugging
     */
    public String getStatusSummary() {
        StringBuilder status = new StringBuilder();
        status.append("方向盘按键: ").append(isEnabled() ? "已启用" : "已禁用").append("\n");
        status.append("监听状态: ").append(isListening ? "监听中" : "未监听").append("\n");
        status.append("车机设备: ");
        if (connectedCarDevice != null) {
            status.append(connectedCarDevice.getName()).append(" (已连接)");
        } else {
            status.append("未检测到");
        }
        status.append("\n\n当前映射:\n");

        // Show current mappings
        Map<ButtonGestureCombination, ButtonAction> mappings = getAllMappings();
        if (mappings.isEmpty()) {
            status.append("无映射配置");
        } else {
            for (Map.Entry<ButtonGestureCombination, ButtonAction> entry : mappings.entrySet()) {
                status.append("• ").append(entry.getKey().getDisplayName())
                      .append(" → ").append(entry.getValue().getDisplayName()).append("\n");
            }
        }

        return status.toString();
    }

    /**
     * Test method to simulate a steering wheel button press
     * This is useful for debugging and testing mappings
     */
    public boolean testButtonPress(SteeringWheelButton button, GestureType gestureType) {
        if (!isEnabled()) {
            LOG.warn("Cannot test button press - steering wheel manager is disabled");
            return false;
        }

        LOG.info("Testing steering wheel button: {} with gesture: {}",
                button.getDisplayName(), gestureType.getDisplayName());

        return executeButtonAction(button, gestureType);
    }
    


    /**
     * Log current system state for debugging
     */
    public void logSystemState() {
        LOG.info("=== 方向盘按键系统状态 ===");
        LOG.info("启用状态: {}", isEnabled());
        LOG.info("监听状态: {}", isListening);
        LOG.info("车机设备: {}", connectedCarDevice != null ? connectedCarDevice.getName() : "未检测到");

        // Log all current mappings
        LOG.info("当前映射配置:");
        Map<ButtonGestureCombination, ButtonAction> mappings = getAllMappings();
        if (mappings.isEmpty()) {
            LOG.info("  无映射配置");
        } else {
            for (Map.Entry<ButtonGestureCombination, ButtonAction> entry : mappings.entrySet()) {
                LOG.info("  {} → {}", entry.getKey().getDisplayName(), entry.getValue().getDisplayName());
            }
        }

        // Log recent button presses
        if (!lastPressTime.isEmpty()) {
            LOG.info("最近按键记录:");
            for (Map.Entry<SteeringWheelButton, Long> entry : lastPressTime.entrySet()) {
                long timeSincePress = System.currentTimeMillis() - entry.getValue();
                LOG.info("  {}: {}秒前", entry.getKey().getDisplayName(), timeSincePress / 1000);
            }
        }
    }

    // ========== DeviceDetectionInterface Implementation ==========

    @Override
    public DetectionResult shouldProcessEvent(KeyEvent event, DetectionMode mode) {
        int keyCode = event.getKeyCode();
        int deviceId = event.getDeviceId();

        // Check if it's a supported steering wheel key
        if (!isCarKeyCode(keyCode)) {
            return new DetectionResult(false, "未知设备", "非方向盘按键", deviceId,
                                     "按键类型检查", "不是支持的方向盘按键: " + keyCode);
        }

        switch (mode) {
            case STRICT:
                return shouldProcessEventStrict(event);
            case NORMAL:
                return shouldProcessEventNormal(event);
            case LENIENT:
                return shouldProcessEventLenient(event);
            case FORCE_ALL:
                return new DetectionResult(true, "强制模式", "方向盘按键", deviceId,
                                         "强制模式", "强制接受所有方向盘按键");
            default:
                return shouldProcessEventNormal(event);
        }
    }

    private DetectionResult shouldProcessEventStrict(KeyEvent event) {
        int deviceId = event.getDeviceId();

        // Must have a connected car device
        if (connectedCarDevice == null) {
            return new DetectionResult(false, "无车机设备", "未连接", deviceId,
                                     "严格模式", "未检测到已连接的车机设备");
        }

        // Must be from known car device
        if (!isFromKnownCarDevice(event)) {
            return new DetectionResult(false, "未知设备", "非车机设备", deviceId,
                                     "严格模式", "事件不是来自已知的车机设备");
        }

        return new DetectionResult(true, connectedCarDevice.getName(), "车机设备", deviceId,
                                 "严格模式", "来自已知车机设备的方向盘按键");
    }

    private DetectionResult shouldProcessEventNormal(KeyEvent event) {
        int deviceId = event.getDeviceId();
        boolean hasCarDevice = connectedCarDevice != null;
        boolean isFromExternal = isFromExternalInputDevice(event);
        boolean isFromKnownCar = isFromKnownCarDevice(event);

        String deviceName = hasCarDevice ? connectedCarDevice.getName() : "外部设备";

        // Priority logic: known car device > car device + external > external device
        if (isFromKnownCar) {
            return new DetectionResult(true, deviceName, "已知车机", deviceId,
                                     "正常模式", "来自已知车机设备");
        } else if (hasCarDevice && isFromExternal) {
            return new DetectionResult(true, deviceName, "车机设备", deviceId,
                                     "正常模式", "有车机连接且来自外部设备");
        } else if (isFromExternal) {
            return new DetectionResult(true, "外部设备", "外部输入", deviceId,
                                     "正常模式", "来自外部输入设备");
        } else {
            return new DetectionResult(false, "内部设备", "虚拟键盘", deviceId,
                                     "正常模式", "来自内部虚拟键盘");
        }
    }

    private DetectionResult shouldProcessEventLenient(KeyEvent event) {
        int deviceId = event.getDeviceId();
        boolean isFromExternal = isFromExternalInputDevice(event);

        if (isFromExternal) {
            String deviceName = connectedCarDevice != null ? connectedCarDevice.getName() : "外部设备";
            return new DetectionResult(true, deviceName, "外部设备", deviceId,
                                     "宽松模式", "接受所有外部设备的方向盘按键");
        } else {
            return new DetectionResult(false, "内部设备", "虚拟键盘", deviceId,
                                     "宽松模式", "拒绝虚拟键盘事件");
        }
    }

    @Override
    public DetectionMode getDetectionMode() {
        String modeStr = preferenceHelper.getString("steering_wheel_detection_mode", DetectionMode.NORMAL.name());
        return DetectionMode.fromString(modeStr);
    }

    @Override
    public void setDetectionMode(DetectionMode mode) {
        this.detectionMode = mode;
        preferenceHelper.setString("steering_wheel_detection_mode", mode.name());
        LOG.info("Steering wheel detection mode set to: {}", mode.getDisplayName());
    }

    @Override
    public DiagnosticInfo getDiagnosticInfo() {
        DetectionMode currentMode = getDetectionMode();
        String connectedInfo = connectedCarDevice != null ?
            connectedCarDevice.getName() + " (" + connectedCarDevice.getAddress() + ")" : "无连接设备";

        return new DiagnosticInfo(
            "方向盘按键管理器",
            isEnabled(),
            isListening,
            currentMode,
            connectedInfo,
            getStatusSummary(),
            getDetailedDeviceInfo()
        );
    }

    @Override
    public String getDetailedDeviceInfo() {
        StringBuilder info = new StringBuilder();
        info.append("=== 方向盘按键详细信息 ===\n");
        info.append("管理器状态: ").append(isEnabled() ? "已启用" : "已禁用").append("\n");
        info.append("监听状态: ").append(isListening ? "监听中" : "未监听").append("\n");
        info.append("检测模式: ").append(getDetectionMode().getDisplayName()).append("\n");

        // Car device information
        if (connectedCarDevice != null) {
            info.append("车机设备: ").append(connectedCarDevice.getName()).append("\n");
            info.append("设备地址: ").append(connectedCarDevice.getAddress()).append("\n");
            info.append("连接状态: ").append(isDeviceConnected(connectedCarDevice) ? "已连接" : "连接状态未知").append("\n");
        } else {
            info.append("车机设备: 未检测到\n");
        }

        // Button statistics
        info.append("按键统计:\n");
        if (lastPressTime.isEmpty()) {
            info.append("  无按键记录\n");
        } else {
            for (Map.Entry<SteeringWheelButton, Long> entry : lastPressTime.entrySet()) {
                long timeSincePress = System.currentTimeMillis() - entry.getValue();
                info.append("  ").append(entry.getKey().getDisplayName())
                    .append(": ").append(timeSincePress / 1000).append("秒前\n");
            }
        }

        // Current mappings
        info.append("当前映射:\n");
        Map<ButtonGestureCombination, ButtonAction> mappings = getAllMappings();
        if (mappings.isEmpty()) {
            info.append("  无映射配置\n");
        } else {
            for (Map.Entry<ButtonGestureCombination, ButtonAction> entry : mappings.entrySet()) {
                info.append("  ").append(entry.getKey().getDisplayName())
                    .append(" → ").append(entry.getValue().getDisplayName()).append("\n");
            }
        }

        return info.toString();
    }

    @Override
    public TestResult testDetection(KeyEvent simulatedEvent) {
        long startTime = System.currentTimeMillis();

        try {
            DetectionMode currentMode = getDetectionMode();
            DetectionResult result = shouldProcessEvent(simulatedEvent, currentMode);
            long responseTime = System.currentTimeMillis() - startTime;

            String message = String.format("检测结果: %s\n设备: %s\n方法: %s\n原因: %s",
                                          result.shouldProcess ? "✅ 会被处理" : "❌ 不会被处理",
                                          result.deviceName, result.detectionMethod, result.reason);

            return new TestResult(true, "检测测试", message, responseTime);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            return new TestResult(false, "检测测试", "测试失败: " + e.getMessage(), responseTime);
        }
    }

    @Override
    public TestResult testFunctionality() {
        long startTime = System.currentTimeMillis();

        try {
            // Test basic functionality
            boolean basicTest = isEnabled() && (isListening || !isEnabled());

            // Test car device detection
            boolean deviceTest = true;
            String deviceInfo = "无车机设备";
            if (connectedCarDevice != null) {
                deviceInfo = "车机设备: " + connectedCarDevice.getName();
                deviceTest = isDeviceConnected(connectedCarDevice);
            }

            // Test mapping configuration
            Map<ButtonGestureCombination, ButtonAction> mappings = getAllMappings();
            boolean mappingTest = !mappings.isEmpty();

            long responseTime = System.currentTimeMillis() - startTime;
            boolean overallSuccess = basicTest && deviceTest && mappingTest;

            String message = String.format("基础功能: %s\n设备检测: %s\n映射配置: %s (%d个)\n%s",
                                          basicTest ? "✅ 正常" : "❌ 异常",
                                          deviceTest ? "✅ 正常" : "❌ 异常",
                                          mappingTest ? "✅ 正常" : "❌ 异常",
                                          mappings.size(), deviceInfo);

            return new TestResult(overallSuccess, "功能测试", message, responseTime);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            return new TestResult(false, "功能测试", "测试失败: " + e.getMessage(), responseTime);
        }
    }

    @Override
    public String getDeviceManagerName() {
        return "方向盘按键管理器";
    }

    @Override
    public boolean isListening() {
        return isListening;
    }

    /**
     * Cleanup resources
     */
    public void cleanup() {
        stopListening();
        lastPressTime.clear();
        pressCount.clear();
        connectedCarDevice = null;
        LOG.info("Steering wheel button manager cleaned up");
    }
}
