<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="filecreation_entries">
        <item>@string/new_file_creation_onceamonth</item>
        <item>@string/new_file_creation_onceaday</item>
        <item>@string/new_file_creation_everystart</item>
        <item>@string/new_file_creation_custom</item>
    </string-array>

    <string-array name="filecreation_values">
        <item>onceamonth</item>
        <item>onceaday</item>
        <item>everystart</item>
        <item>custom</item>
    </string-array>


    <string-array name="autoemail_presets">
        <item>@string/emailprovider_google</item>
        <item>@string/emailprovider_msn</item>
        <item>@string/emailprovider_yahoo</item>
        <item>@string/emailprovider_manual</item>
    </string-array>

    <string-array name="autoemail_presetvalues">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>99</item>
    </string-array>


    <string-array name="osm_visibility_choices">
        <item>private</item>
        <item>public</item>
        <item>trackable</item>
        <item>identifiable</item>
    </string-array>

    <string-array name="opengts_server_communication_methods_values">
        <item>HTTP</item>
        <item>HTTPS</item>
        <item>UDP</item>
    </string-array>

    <string-array name="autoftp_ssltls_entries">
        <item>@string/autoftp_ssltls_none</item>
        <item>@string/autoftp_ssltls_ssl</item>
        <item>@string/autoftp_ssltls_tls</item>
    </string-array>
    <string-array name="autoftp_ssltls_values">
        <item> </item>
        <item>SSL</item>
        <item>TLS</item>
    </string-array>

    <string-array name="gps_main_views">
        <item>@string/view_simple</item>
        <item>@string/view_detailed</item>
        <item>@string/view_big</item>
        <item>@string/view_log</item>
        <item>@string/view_annotation</item>
    </string-array>


    <string-array name="app_theme_options">
        <item>@string/app_theme_follow_system</item>
        <item>@string/app_theme_light</item>
        <item>@string/app_theme_dark</item>
    </string-array>

    <string-array name="app_theme_values">
        <item>system</item>
        <item>light</item>
        <item>dark</item>
    </string-array>

    <!-- Annotation layout style options -->
    <string-array name="annotation_layout_style_entries">
        <item>Rectangular buttons</item>
        <item>Circular buttons</item>
    </string-array>

    <string-array name="annotation_layout_style_values">
        <item>rectangular</item>
        <item>circular</item>
    </string-array>

    <!-- Annotation spacing mode options -->
    <string-array name="annotation_spacing_mode_entries">
        <item>Compact (2dp)</item>
        <item>Standard (4dp)</item>
        <item>Loose (8dp)</item>
    </string-array>

    <string-array name="annotation_spacing_mode_values">
        <item>compact</item>
        <item>standard</item>
        <item>loose</item>
    </string-array>

    <!-- Annotation view mode options -->
    <string-array name="annotation_view_mode_entries">
        <item>Grid view</item>
        <item>List view</item>
        <item>Grouped view</item>
    </string-array>

    <string-array name="annotation_view_mode_values">
        <item>grid</item>
        <item>list</item>
        <item>grouped</item>
    </string-array>

    <!-- Keyboard detection mode options -->
    <string-array name="keyboard_detection_mode_entries">
        <item>严格模式</item>
        <item>正常模式</item>
        <item>宽松模式</item>
        <item>强制模式</item>
    </string-array>

    <string-array name="keyboard_detection_mode_values">
        <item>STRICT</item>
        <item>NORMAL</item>
        <item>LENIENT</item>
        <item>FORCE_ALL</item>
    </string-array>

    <string-array name="haptic_feedback_intensity_entries">
        <item>低强度</item>
        <item>中强度</item>
        <item>高强度</item>
    </string-array>

    <string-array name="haptic_feedback_intensity_values">
        <item>LOW</item>
        <item>MEDIUM</item>
        <item>HIGH</item>
    </string-array>

    <string-array name="audio_feedback_type_entries">
        <item>无声音</item>
        <item>系统通知音</item>
        <item>系统铃声</item>
        <item>系统闹钟音</item>
        <item>标准哔声</item>
        <item>点击音</item>
        <item>错误音</item>
        <item>成功音</item>
        <item>警告音</item>
        <item>按键音0</item>
        <item>按键音1</item>
        <item>按键音*</item>
        <item>自定义音频</item>
    </string-array>

    <string-array name="audio_feedback_type_values">
        <item>NONE</item>
        <item>SYSTEM_NOTIFICATION</item>
        <item>SYSTEM_RINGTONE</item>
        <item>SYSTEM_ALARM</item>
        <item>TONE_BEEP</item>
        <item>TONE_CLICK</item>
        <item>TONE_ERROR</item>
        <item>TONE_SUCCESS</item>
        <item>TONE_WARNING</item>
        <item>TONE_DTMF_0</item>
        <item>TONE_DTMF_1</item>
        <item>TONE_DTMF_STAR</item>
        <item>CUSTOM</item>
    </string-array>


</resources>