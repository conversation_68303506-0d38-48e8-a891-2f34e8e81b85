/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import androidx.core.content.ContextCompat;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.common.PreferenceHelper;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Locale;

/**
 * 简化的语音输入管理器
 * 只处理语音转文字功能，使用传统Android Intent方式调用讯飞语记
 * 移除了音频录制功能和复杂的统一处理器
 */
public class VoiceInputManager {
    
    private static final Logger LOG = Logs.of(VoiceInputManager.class);
    public static final int VOICE_INPUT_REQUEST_CODE = 1001;
    
    private final Activity activity;
    private final androidx.fragment.app.Fragment fragment;
    private final PreferenceHelper preferenceHelper;
    private VoiceInputListener listener;
    private boolean isRecording = false;

    /**
     * Interface for voice input events
     */
    public interface VoiceInputListener {
        void onVoiceInputStarted();
        void onVoiceInputResult(String text);
        void onVoiceInputError(String error);
        void onVoiceInputCancelled();
    }

    /**
     * Constructor for Fragment-based usage
     */
    public VoiceInputManager(androidx.fragment.app.Fragment fragment, VoiceInputListener listener) {
        this.fragment = fragment;
        this.activity = fragment.getActivity();
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        LOG.debug("VoiceInputManager initialized with Fragment");
    }

    /**
     * Constructor for Activity-based usage
     */
    public VoiceInputManager(Activity activity, VoiceInputListener listener) {
        this.activity = activity;
        this.fragment = null;
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        LOG.debug("VoiceInputManager initialized with Activity");
    }

    /**
     * Check if voice input is available on this device
     */
    public boolean isVoiceInputAvailable() {
        try {
            if (activity == null) {
                LOG.warn("Activity is null, voice input not available");
                return false;
            }
            
            PackageManager pm = activity.getPackageManager();
            if (pm == null) {
                LOG.warn("PackageManager is null, voice input not available");
                return false;
            }

            // Check if speech recognition is available
            boolean speechRecognitionAvailable = SpeechRecognizer.isRecognitionAvailable(activity);
            LOG.debug("Speech recognition available: {}", speechRecognitionAvailable);
            
            return speechRecognitionAvailable;
            
        } catch (Exception e) {
            LOG.error("Error checking voice input availability", e);
            return false;
        }
    }

    /**
     * Check if the app has RECORD_AUDIO permission
     */
    public boolean hasRecordAudioPermission() {
        if (activity == null) {
            LOG.warn("Activity is null, cannot check permission");
            return false;
        }
        
        int permission = ContextCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO);
        boolean hasPermission = permission == PackageManager.PERMISSION_GRANTED;
        LOG.debug("RECORD_AUDIO permission granted: {}", hasPermission);
        return hasPermission;
    }

    /**
     * Start voice input using traditional Android Intent approach
     */
    public boolean startVoiceInput() {
        if (isRecording) {
            LOG.warn("Voice input already in progress");
            return false;
        }

        if (!preferenceHelper.isVoiceInputEnabled()) {
            LOG.debug("Voice input is disabled in settings");
            return false;
        }

        if (!isVoiceInputAvailable()) {
            LOG.warn("Voice input is not available on this device");
            if (listener != null) {
                listener.onVoiceInputError("设备不支持语音识别功能");
            }
            return false;
        }

        if (!hasRecordAudioPermission()) {
            LOG.warn("RECORD_AUDIO permission not granted");
            if (listener != null) {
                listener.onVoiceInputError("需要录音权限才能使用语音输入");
            }
            return false;
        }

        try {
            Intent intent = createVoiceInputIntent();

            // Start voice input activity
            LOG.debug("Starting voice input activity");
            if (fragment != null) {
                fragment.startActivityForResult(intent, VOICE_INPUT_REQUEST_CODE);
                LOG.debug("Started via Fragment.startActivityForResult");
            } else {
                activity.startActivityForResult(intent, VOICE_INPUT_REQUEST_CODE);
                LOG.debug("Started via Activity.startActivityForResult");
            }
            
            isRecording = true;

            if (listener != null) {
                listener.onVoiceInputStarted();
            }

            LOG.debug("Voice input started successfully");
            return true;

        } catch (android.content.ActivityNotFoundException e) {
            LOG.error("No activity found to handle voice input intent", e);
            if (listener != null) {
                listener.onVoiceInputError("未找到语音识别应用。请安装讯飞语记、Google语音输入或其他语音识别应用。");
            }
            return false;
        } catch (Exception e) {
            LOG.error("Error starting voice input", e);
            if (listener != null) {
                listener.onVoiceInputError("启动语音识别失败: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Create voice input intent
     */
    private Intent createVoiceInputIntent() {
        Intent intent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);

        // Set language
        String language = preferenceHelper.getVoiceInputLanguage();
        if (language == null || language.isEmpty()) {
            language = Locale.getDefault().getLanguage();
        }
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, language);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, language);

        // Set prompts and other options
        intent.putExtra(RecognizerIntent.EXTRA_PROMPT, "请说出注释内容");
        intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5);
        intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, activity.getPackageName());

        // Set timeout if specified
        int timeout = preferenceHelper.getVoiceInputTimeout();
        if (timeout > 0) {
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
        }

        LOG.debug("Created voice input intent with language: {}, timeout: {}", language, timeout);
        return intent;
    }

    /**
     * Handle activity result from voice input
     */
    public void handleActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode != VOICE_INPUT_REQUEST_CODE) {
            return;
        }

        isRecording = false;
        LOG.debug("Voice input activity result: requestCode={}, resultCode={}", requestCode, resultCode);

        if (resultCode == Activity.RESULT_OK && data != null) {
            ArrayList<String> results = data.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS);
            if (results != null && !results.isEmpty()) {
                String recognizedText = results.get(0);
                LOG.info("Voice input result: '{}'", recognizedText);
                
                if (listener != null) {
                    listener.onVoiceInputResult(recognizedText);
                }
            } else {
                LOG.warn("Voice input returned empty results");
                if (listener != null) {
                    listener.onVoiceInputError("语音识别没有结果");
                }
            }
        } else if (resultCode == Activity.RESULT_CANCELED) {
            LOG.debug("Voice input was cancelled");
            if (listener != null) {
                listener.onVoiceInputCancelled();
            }
        } else {
            LOG.warn("Voice input failed with result code: {}", resultCode);
            if (listener != null) {
                listener.onVoiceInputError("语音识别失败");
            }
        }
    }

    /**
     * Stop voice input (if needed)
     */
    public void stopVoiceInput() {
        if (isRecording) {
            LOG.debug("Stopping voice input");
            isRecording = false;
        }
    }

    /**
     * Check if voice input is currently recording
     */
    public boolean isRecording() {
        return isRecording;
    }

    /**
     * Set voice input listener
     */
    public void setVoiceInputListener(VoiceInputListener listener) {
        this.listener = listener;
    }
}
