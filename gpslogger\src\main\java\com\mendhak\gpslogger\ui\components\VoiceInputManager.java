/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import androidx.core.content.ContextCompat;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.common.PreferenceHelper;
import org.slf4j.Logger;

import java.util.Locale;

/**
 * Manages voice input functionality for annotation buttons
 */
public class VoiceInputManager {
    
    private static final Logger LOG = Logs.of(VoiceInputManager.class);
    public static final int VOICE_INPUT_REQUEST_CODE = 1001;
    
    private final Activity activity;
    private final androidx.fragment.app.Fragment fragment;
    private final PreferenceHelper preferenceHelper;
    private VoiceInputListener listener;
    private boolean isRecording = false;
    private UnifiedVoiceInputManager unifiedVoiceInputManager;
    private String pendingAnnotationText; // Store annotation text for audio filename

    // 配置选项：使用统一音频处理器
    private static final boolean USE_UNIFIED_PROCESSOR = true;
    
    /**
     * Interface for voice input callbacks
     */
    public interface VoiceInputListener {
        void onVoiceInputStarted();
        void onVoiceInputResult(String text);
        void onVoiceInputError(String error);
        void onVoiceInputCancelled();
    }
    
    public VoiceInputManager(androidx.fragment.app.Fragment fragment, VoiceInputListener listener) {
        this.fragment = fragment;
        this.activity = fragment.getActivity();
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        initializeVoiceInput();
    }

    /**
     * Constructor for Activity-based usage
     */
    public VoiceInputManager(Activity activity, VoiceInputListener listener) {
        this.activity = activity;
        this.fragment = null;
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        initializeVoiceInput();
    }

    /**
     * Initialize voice input manager
     */
    private void initializeVoiceInput() {
        Context context = activity != null ? activity : (fragment != null ? fragment.getContext() : null);
        System.out.println("=== VoiceInputManager: initializeVoiceInput called ===");
        LOG.info("=== VoiceInputManager: initializeVoiceInput called ===");

        if (context != null) {
            if (USE_UNIFIED_PROCESSOR) {
                // 使用新的统一音频处理器（只进行语音识别，不录音）
                System.out.println("=== VoiceInputManager: Creating UnifiedVoiceInputManager ===");
                LOG.info("Using unified audio processor for speech recognition only");

                UnifiedVoiceInputManager.VoiceInputListener unifiedListener = new UnifiedVoiceInputManager.VoiceInputListener() {
                    @Override
                    public void onVoiceInputStarted() {
                        LOG.debug("Unified voice input started");
                        if (listener != null) {
                            listener.onVoiceInputStarted();
                        }
                    }

                    @Override
                    public void onVoiceInputResult(String text) {
                        LOG.info("Unified voice input result: '{}'", text);
                        if (listener != null) {
                            listener.onVoiceInputResult(text);
                        }
                    }

                    @Override
                    public void onVoiceInputError(String error) {
                        LOG.error("Unified voice input error: {}", error);
                        if (listener != null) {
                            listener.onVoiceInputError(error);
                        }
                    }

                    @Override
                    public void onVoiceInputCancelled() {
                        LOG.debug("Unified voice input cancelled");
                        if (listener != null) {
                            listener.onVoiceInputCancelled();
                        }
                    }
                };

                if (fragment != null) {
                    unifiedVoiceInputManager = new UnifiedVoiceInputManager(fragment, unifiedListener);
                } else {
                    unifiedVoiceInputManager = new UnifiedVoiceInputManager(activity, unifiedListener);
                }

            } else {
                LOG.warn("Traditional voice input mode disabled - only unified processor supported");
            }
        } else {
            LOG.warn("Cannot initialize voice input - no context available");
        }
    }
    
    /**
     * Check if voice input is available on this device
     */
    public boolean isVoiceInputAvailable() {
        try {
            if (activity == null) {
                LOG.warn("Activity is null, voice input not available");
                return false;
            }
            PackageManager pm = activity.getPackageManager();
            if (pm == null) {
                LOG.warn("PackageManager is null, voice input not available");
                return false;
            }

            // Method 1: Check using standard RecognizerIntent
            Intent intent1 = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            java.util.List<android.content.pm.ResolveInfo> activities1 = pm.queryIntentActivities(intent1, 0);
            LOG.debug("Standard RecognizerIntent check: found " + activities1.size() + " activities");

            // Method 2: Check with more specific intent
            Intent intent2 = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            intent2.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
            java.util.List<android.content.pm.ResolveInfo> activities2 = pm.queryIntentActivities(intent2, 0);
            LOG.debug("Specific RecognizerIntent check: found " + activities2.size() + " activities");

            // Method 3: Check using PackageManager.MATCH_DEFAULT_ONLY flag
            Intent intent3 = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            java.util.List<android.content.pm.ResolveInfo> activities3 = pm.queryIntentActivities(intent3, PackageManager.MATCH_DEFAULT_ONLY);
            LOG.debug("Default-only RecognizerIntent check: found " + activities3.size() + " activities");

            // Method 4: Check for SpeechRecognizer service availability
            boolean speechRecognizerAvailable = android.speech.SpeechRecognizer.isRecognitionAvailable(activity);
            LOG.debug("SpeechRecognizer.isRecognitionAvailable: " + speechRecognizerAvailable);

            // Method 5: Check for specific known speech recognition apps
            boolean knownAppsAvailable = checkKnownSpeechApps(pm);
            LOG.debug("Known speech apps available: " + knownAppsAvailable);

            // Combine all methods
            boolean available = activities1.size() > 0 || activities2.size() > 0 || activities3.size() > 0 || speechRecognizerAvailable || knownAppsAvailable;

            LOG.debug("Final voice input availability: " + available);

            // Log all available speech recognition apps for debugging
            java.util.Set<String> allApps = new java.util.HashSet<>();
            for (android.content.pm.ResolveInfo info : activities1) {
                allApps.add(info.activityInfo.packageName);
            }
            for (android.content.pm.ResolveInfo info : activities2) {
                allApps.add(info.activityInfo.packageName);
            }
            for (android.content.pm.ResolveInfo info : activities3) {
                allApps.add(info.activityInfo.packageName);
            }

            for (String packageName : allApps) {
                LOG.debug("Available speech recognition app: " + packageName);
            }

            return available;
        } catch (Exception e) {
            LOG.error("Error checking voice input availability", e);
            return false;
        }
    }

    /**
     * Check for known speech recognition apps
     */
    private boolean checkKnownSpeechApps(PackageManager pm) {
        String[] knownSpeechApps = {
            "com.google.android.googlequicksearchbox", // Google App
            "com.google.android.voicesearch", // Google Voice Search
            "com.iflytek.speechcloud", // 讯飞语记
            "com.iflytek.inputmethod", // 讯飞输入法
            "com.baidu.input", // 百度输入法
            "com.sohu.inputmethod.sogou", // 搜狗输入法
            "com.samsung.android.bixby.agent", // Samsung Bixby
            "com.microsoft.cortana", // Microsoft Cortana
            "com.amazon.dee.app" // Amazon Alexa
        };

        for (String packageName : knownSpeechApps) {
            try {
                pm.getPackageInfo(packageName, 0);
                LOG.debug("Found known speech app: " + packageName);
                return true;
            } catch (PackageManager.NameNotFoundException e) {
                // App not installed, continue checking
            }
        }

        return false;
    }
    
    /**
     * Check if RECORD_AUDIO permission is granted
     */
    public boolean hasRecordAudioPermission() {
        return ContextCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * Start voice input if conditions are met
     */
    public boolean startVoiceInput() {
        System.out.println("=== VoiceInputManager: startVoiceInput called ===");
        LOG.debug("Starting voice input");

        if (activity == null) {
            LOG.error("Activity is null, cannot start voice input");
            return false;
        }

        if (preferenceHelper == null) {
            LOG.error("PreferenceHelper is null, cannot start voice input");
            return false;
        }

        // Check if voice input is enabled in settings
        if (!preferenceHelper.isVoiceInputEnabled()) {
            LOG.debug("Voice input is disabled in settings");
            return false;
        }

        // 使用统一音频处理器
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            LOG.info("Starting voice input with unified audio processor (speech recognition only)");
            return unifiedVoiceInputManager.startVoiceInput();
        }

        // 如果没有使用统一处理器，则回退到传统方式（但不推荐）
        LOG.warn("Unified processor not available, falling back to traditional voice input (speech recognition only)");

        try {
            Intent intent = createVoiceInputIntent();

            // Try to start the voice input activity using Fragment
            LOG.debug("Attempting to start voice input activity via Fragment");
            if (fragment != null) {
                fragment.startActivityForResult(intent, VOICE_INPUT_REQUEST_CODE);
                LOG.debug("Started via Fragment.startActivityForResult");
            } else {
                activity.startActivityForResult(intent, VOICE_INPUT_REQUEST_CODE);
                LOG.debug("Started via Activity.startActivityForResult (fallback)");
            }
            isRecording = true;

            if (listener != null) {
                listener.onVoiceInputStarted();
            }

            LOG.debug("Voice input started successfully");
            return true;

        } catch (android.content.ActivityNotFoundException e) {
            LOG.error("No activity found to handle voice input intent", e);
            if (listener != null) {
                listener.onVoiceInputError("未找到语音识别应用。请安装讯飞语记、Google语音输入或其他语音识别应用。");
            }
            return false;
        } catch (Exception e) {
            LOG.error("Error starting voice input", e);
            if (listener != null) {
                listener.onVoiceInputError("启动语音识别失败: " + e.getMessage() + "。请检查语音识别应用是否正常工作。");
            }
            return false;
        }
    }
    
    /**
     * Create the voice input intent
     */
    private Intent createVoiceInputIntent() {
        Intent intent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);

        // Set language preference
        String language = preferenceHelper.getVoiceInputLanguage();
        if (language.isEmpty()) {
            language = Locale.getDefault().toString();
        }
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, language);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, language);

        // Set prompts and other options
        intent.putExtra(RecognizerIntent.EXTRA_PROMPT, "请说出注释内容");
        intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5); // Increase max results for better accuracy
        intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, activity.getPackageName());

        // Set timeout if specified
        int timeout = preferenceHelper.getVoiceInputTimeout();
        if (timeout > 0) {
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
        }

        // Add additional compatibility flags
        intent.addFlags(Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);

        LOG.debug("Created voice input intent with language: " + language + ", timeout: " + timeout + "s");

        return intent;
    }
    
    /**
     * Handle the result from voice input activity
     */
    public void handleActivityResult(int requestCode, int resultCode, Intent data) {
        LOG.debug("=== VOICE INPUT ACTIVITY RESULT ===");
        LOG.debug("Request code: " + requestCode + " (expected: " + VOICE_INPUT_REQUEST_CODE + " or Xunfei: " + VoiceRecognitionAdapter.getXunfeiRequestCode() + ")");
        LOG.debug("Result code: " + resultCode);
        LOG.debug("Data: " + (data != null ? "not null" : "null"));

        // 首先检查是否是统一处理器的结果（包括直接调用讯飞语记）
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            unifiedVoiceInputManager.handleActivityResult(requestCode, resultCode, data);
            return;
        }

        // 处理传统的语音输入结果
        if (requestCode != VOICE_INPUT_REQUEST_CODE) {
            LOG.debug("Request code mismatch, ignoring");
            return;
        }

        isRecording = false;
        LOG.debug("Set isRecording to false");

        if (resultCode == Activity.RESULT_OK && data != null) {
            try {
                java.util.ArrayList<String> results = data.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS);
                LOG.debug("Extracted results: " + (results != null ? results.size() + " items" : "null"));

                if (results != null && !results.isEmpty()) {
                    String recognizedText = results.get(0);
                    LOG.info("=== VOICE RECOGNITION SUCCESS ===");
                    LOG.info("Recognized text: '" + recognizedText + "'");
                    LOG.info("All results: " + results.toString());

                    // Store the recognized text for later use
                    pendingAnnotationText = recognizedText;

                    if (listener != null) {
                        LOG.debug("Calling listener.onVoiceInputResult()");
                        listener.onVoiceInputResult(recognizedText);
                        LOG.debug("Listener callback completed");
                    } else {
                        LOG.warn("Listener is null, cannot deliver result");
                    }
                } else {
                    LOG.warn("Voice input returned empty results");
                    if (listener != null) {
                        listener.onVoiceInputError("语音识别没有结果，请重试");
                    }
                }
            } catch (Exception e) {
                LOG.error("Error processing voice input result", e);
                if (listener != null) {
                    listener.onVoiceInputError("处理语音识别结果时出错: " + e.getMessage());
                }
            }
        } else if (resultCode == Activity.RESULT_CANCELED) {
            LOG.debug("Voice input was cancelled by user");
            if (listener != null) {
                listener.onVoiceInputCancelled();
            }
        } else {
            LOG.warn("Voice input failed with result code: " + resultCode);
            if (listener != null) {
                listener.onVoiceInputError("语音识别失败，请重试");
            }
        }
        LOG.debug("=== VOICE INPUT RESULT PROCESSING COMPLETE ===");
    }
    
    /**
     * Stop voice input if currently recording
     */
    public void stopVoiceInput() {
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            LOG.debug("Stopping unified voice input");
            unifiedVoiceInputManager.stopVoiceInput();
        }

        if (isRecording) {
            isRecording = false;
            LOG.debug("Voice input stopped");
        }
    }
    
    /**
     * Check if currently recording
     */
    public boolean isRecording() {
        return isRecording;
    }
    
    /**
     * Set the voice input listener
     */
    public void setListener(VoiceInputListener listener) {
        this.listener = listener;
    }





    /**
     * Get the pending annotation text (the original voice recognition result)
     */
    public String getPendingAnnotationText() {
        return pendingAnnotationText;
    }

    /**
     * Clear the pending annotation text
     */
    public void clearPendingAnnotationText() {
        pendingAnnotationText = null;
    }
}
